using System.ComponentModel.DataAnnotations;

namespace Net9.QueryStock.Server.Models
{
    /// <summary>
    /// 股票基本資訊實體
    /// </summary>
    public class Stock
    {
        public Guid Id { get; set; }
        
        /// <summary>
        /// 股票代號 (例如: AAPL, TSLA)
        /// </summary>
        [Required]
        [StringLength(20)]
        public string Symbol { get; set; } = string.Empty;
        
        /// <summary>
        /// 公司名稱
        /// </summary>
        [Required]
        [StringLength(200)]
        public string Name { get; set; } = string.Empty;
        
        /// <summary>
        /// 交易所代碼 (例如: NASDAQ, NYSE)
        /// </summary>
        [StringLength(50)]
        public string? Exchange { get; set; }
        
        /// <summary>
        /// 貨幣代碼 (例如: USD, TWD)
        /// </summary>
        [StringLength(10)]
        public string Currency { get; set; } = "USD";
        
        /// <summary>
        /// 股票類型 (例如: Common Stock, ETF)
        /// </summary>
        [StringLength(50)]
        public string? StockType { get; set; }
        
        /// <summary>
        /// 所屬行業
        /// </summary>
        [StringLength(100)]
        public string? Industry { get; set; }
        
        /// <summary>
        /// 所屬國家
        /// </summary>
        [StringLength(50)]
        public string? Country { get; set; }
        
        /// <summary>
        /// 是否啟用
        /// </summary>
        public bool IsActive { get; set; } = true;
        
        /// <summary>
        /// 建立時間
        /// </summary>
        public DateTimeOffset CreatedAt { get; set; } = DateTimeOffset.UtcNow;
        
        /// <summary>
        /// 最後更新時間
        /// </summary>
        public DateTimeOffset UpdatedAt { get; set; } = DateTimeOffset.UtcNow;
        
        /// <summary>
        /// 股票價格歷史記錄
        /// </summary>
        public virtual ICollection<StockPrice> StockPrices { get; set; } = new List<StockPrice>();
        
        /// <summary>
        /// 股票詳細資訊
        /// </summary>
        public virtual StockInfo? StockInfo { get; set; }
    }
}
