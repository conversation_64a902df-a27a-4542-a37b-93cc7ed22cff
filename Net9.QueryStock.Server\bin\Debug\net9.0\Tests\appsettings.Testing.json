{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning", "Microsoft.EntityFrameworkCore": "Information"}}, "ConnectionStrings": {"DefaultConnection": "Server=(localdb)\\mssqllocaldb;Database=Net9QueryStockTest;Trusted_Connection=true;MultipleActiveResultSets=true"}, "Finnhub": {"BaseUrl": "https://finnhub.io/api/v1", "TimeoutSeconds": 30, "IsEnabled": true}, "AlphaVantage": {"BaseUrl": "https://www.alphavantage.co/query", "TimeoutSeconds": 30, "IsEnabled": false}, "YahooFinance": {"BaseUrl": "https://query1.finance.yahoo.com/v8/finance/chart", "TimeoutSeconds": 30, "IsEnabled": false}, "ApiKeyEncryption": {"Key": "TestEncryptionKey123456789012345"}, "Jwt": {"Key": "TestJwtSecretKey123456789012345678901234567890", "Issuer": "Net9.QueryStock.Test", "Audience": "Net9.QueryStock.Test.Client", "ExpirationHours": 24}, "TestSettings": {"UseRealApiKeys": false, "FinnhubTestApiKey": "YOUR_ACTUAL_FINNHUB_API_KEY_HERE", "SkipExternalApiTests": true, "DatabaseCleanupEnabled": true, "TestDataSeedEnabled": true}}