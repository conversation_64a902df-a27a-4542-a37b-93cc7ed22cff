using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Security.Claims;
using Net9.QueryStock.Server.DTOs;
using Net9.QueryStock.Server.Interfaces;

namespace Net9.QueryStock.Server.Controllers
{
    /// <summary>
    /// 認證控制器
    /// </summary>
    [Route("api/[controller]")]
    [ApiController]
    public class AuthController : ControllerBase
    {
        private readonly IAuthService _authService;
        private readonly ILogger<AuthController> _logger;

        public AuthController(
            IAuthService authService,
            ILogger<AuthController> logger)
        {
            _authService = authService;
            _logger = logger;
        }

        /// <summary>
        /// 用戶註冊
        /// </summary>
        [HttpPost("register")]
        public async Task<ActionResult<AuthResponse>> Register([FromBody] RegisterRequest request)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    var errors = ModelState.Values
                        .SelectMany(v => v.Errors)
                        .Select(e => e.ErrorMessage)
                        .ToList();

                    return BadRequest(new AuthResponse
                    {
                        Success = false,
                        Message = string.Join("; ", errors)
                    });
                }

                var result = await _authService.RegisterAsync(request);
                
                if (result.Success)
                {
                    return Ok(result);
                }
                else
                {
                    return BadRequest(result);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "註冊時發生錯誤");
                return StatusCode(500, new AuthResponse
                {
                    Success = false,
                    Message = "註冊時發生內部錯誤"
                });
            }
        }

        /// <summary>
        /// 用戶登入
        /// </summary>
        [HttpPost("login")]
        public async Task<ActionResult<AuthResponse>> Login([FromBody] LoginRequest request)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    var errors = ModelState.Values
                        .SelectMany(v => v.Errors)
                        .Select(e => e.ErrorMessage)
                        .ToList();

                    return BadRequest(new AuthResponse
                    {
                        Success = false,
                        Message = string.Join("; ", errors)
                    });
                }

                var result = await _authService.LoginAsync(request);
                
                if (result.Success)
                {
                    return Ok(result);
                }
                else
                {
                    return Unauthorized(result);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "登入時發生錯誤");
                return StatusCode(500, new AuthResponse
                {
                    Success = false,
                    Message = "登入時發生內部錯誤"
                });
            }
        }

        /// <summary>
        /// 獲取當前用戶資訊
        /// </summary>
        [HttpGet("me")]
        [Authorize]
        public async Task<ActionResult<UserDto>> GetCurrentUser()
        {
            try
            {
                var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier);
                if (userIdClaim == null || !Guid.TryParse(userIdClaim.Value, out var userId))
                {
                    return Unauthorized(new { message = "無效的權杖" });
                }

                var user = await _authService.GetUserByIdAsync(userId);
                if (user == null)
                {
                    return NotFound(new { message = "找不到用戶" });
                }

                return Ok(user);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "獲取用戶資訊時發生錯誤");
                return StatusCode(500, new { message = "獲取用戶資訊時發生內部錯誤" });
            }
        }

        /// <summary>
        /// 修改密碼
        /// </summary>
        [HttpPost("change-password")]
        [Authorize]
        public async Task<ActionResult<AuthResponse>> ChangePassword([FromBody] ChangePasswordRequest request)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    var errors = ModelState.Values
                        .SelectMany(v => v.Errors)
                        .Select(e => e.ErrorMessage)
                        .ToList();

                    return BadRequest(new AuthResponse
                    {
                        Success = false,
                        Message = string.Join("; ", errors)
                    });
                }

                var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier);
                if (userIdClaim == null || !Guid.TryParse(userIdClaim.Value, out var userId))
                {
                    return Unauthorized(new AuthResponse
                    {
                        Success = false,
                        Message = "無效的權杖"
                    });
                }

                var result = await _authService.ChangePasswordAsync(userId, request);
                
                if (result.Success)
                {
                    return Ok(result);
                }
                else
                {
                    return BadRequest(result);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "修改密碼時發生錯誤");
                return StatusCode(500, new AuthResponse
                {
                    Success = false,
                    Message = "修改密碼時發生內部錯誤"
                });
            }
        }

        /// <summary>
        /// 驗證權杖
        /// </summary>
        [HttpPost("validate")]
        public async Task<ActionResult<UserDto>> ValidateToken([FromBody] ValidateTokenRequest request)
        {
            try
            {
                if (string.IsNullOrEmpty(request.Token))
                {
                    return BadRequest(new { message = "權杖不能為空" });
                }

                var user = await _authService.ValidateTokenAsync(request.Token);
                if (user == null)
                {
                    return Unauthorized(new { message = "無效的權杖" });
                }

                return Ok(user);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "驗證權杖時發生錯誤");
                return StatusCode(500, new { message = "驗證權杖時發生內部錯誤" });
            }
        }

        /// <summary>
        /// 登出（客戶端處理，伺服器端無需特殊處理）
        /// </summary>
        [HttpPost("logout")]
        [Authorize]
        public ActionResult Logout()
        {
            // JWT 是無狀態的，登出主要由客戶端處理（刪除本地存儲的權杖）
            return Ok(new { message = "登出成功" });
        }
    }

    /// <summary>
    /// 驗證權杖請求
    /// </summary>
    public class ValidateTokenRequest
    {
        public string Token { get; set; } = string.Empty;
    }
}
