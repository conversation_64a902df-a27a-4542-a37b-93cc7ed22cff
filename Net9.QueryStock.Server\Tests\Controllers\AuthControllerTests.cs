using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Mvc.Testing;
using Microsoft.Extensions.DependencyInjection;
using Net9.QueryStock.Server.Data;
using Net9.QueryStock.Server.DTOs;
using System.Net.Http.Json;
using System.Text.Json;
using Xunit;

namespace Net9.QueryStock.Server.Tests.Controllers
{
    /// <summary>
    /// 認證控制器整合測試
    /// 測試完整的 HTTP 請求流程
    /// </summary>
    public class AuthControllerTests : IClassFixture<WebApplicationFactory<Program>>
    {
        private readonly WebApplicationFactory<Program> _factory;
        private readonly HttpClient _client;

        public AuthControllerTests(WebApplicationFactory<Program> factory)
        {
            _factory = factory.WithWebHostBuilder(builder =>
            {
                builder.UseEnvironment("Testing");
                builder.ConfigureServices(services =>
                {
                    // 使用測試資料庫
                    var descriptor = services.SingleOrDefault(d => d.ServiceType == typeof(DbContextOptions<Net9QueryStockServerContext>));
                    if (descriptor != null)
                    {
                        services.Remove(descriptor);
                    }

                    services.AddDbContext<Net9QueryStockServerContext>(options =>
                    {
                        options.UseSqlServer("Server=(localdb)\\mssqllocaldb;Database=Net9QueryStockIntegrationTest;Trusted_Connection=true;MultipleActiveResultSets=true");
                    });
                });
            });

            _client = _factory.CreateClient();
            
            // 確保測試資料庫存在
            using var scope = _factory.Services.CreateScope();
            var context = scope.ServiceProvider.GetRequiredService<Net9QueryStockServerContext>();
            context.Database.EnsureCreated();
        }

        [Fact]
        public async Task Register_WithValidData_ReturnsSuccessAndToken()
        {
            // Arrange
            await CleanupTestData();
            var registerRequest = new RegisterRequest
            {
                Username = "integrationtestuser",
                Password = "password123",
                ConfirmPassword = "password123",
                Email = "<EMAIL>"
            };

            // Act
            var response = await _client.PostAsJsonAsync("/api/auth/register", registerRequest);

            // Assert
            response.EnsureSuccessStatusCode();
            var content = await response.Content.ReadAsStringAsync();
            var authResponse = JsonSerializer.Deserialize<AuthResponse>(content, new JsonSerializerOptions { PropertyNameCaseInsensitive = true });
            
            Assert.NotNull(authResponse);
            Assert.True(authResponse.Success);
            Assert.NotNull(authResponse.Token);
            Assert.NotNull(authResponse.User);
            Assert.Equal("integrationtestuser", authResponse.User.Username);
        }

        [Fact]
        public async Task Register_WithDuplicateUsername_ReturnsBadRequest()
        {
            // Arrange
            await CleanupTestData();
            
            // 先註冊一個用戶
            var firstRequest = new RegisterRequest
            {
                Username = "duplicateuser",
                Password = "password123",
                ConfirmPassword = "password123",
                Email = "<EMAIL>"
            };
            await _client.PostAsJsonAsync("/api/auth/register", firstRequest);

            // 嘗試註冊相同用戶名
            var secondRequest = new RegisterRequest
            {
                Username = "duplicateuser",
                Password = "differentpassword",
                ConfirmPassword = "differentpassword",
                Email = "<EMAIL>"
            };

            // Act
            var response = await _client.PostAsJsonAsync("/api/auth/register", secondRequest);

            // Assert
            Assert.Equal(System.Net.HttpStatusCode.BadRequest, response.StatusCode);
            var content = await response.Content.ReadAsStringAsync();
            var authResponse = JsonSerializer.Deserialize<AuthResponse>(content, new JsonSerializerOptions { PropertyNameCaseInsensitive = true });
            
            Assert.NotNull(authResponse);
            Assert.False(authResponse.Success);
            Assert.Contains("用戶名稱已存在", authResponse.Message);
        }

        [Fact]
        public async Task Login_WithValidCredentials_ReturnsSuccessAndToken()
        {
            // Arrange
            await CleanupTestData();
            
            // 先註冊用戶
            var registerRequest = new RegisterRequest
            {
                Username = "logintest",
                Password = "password123",
                ConfirmPassword = "password123",
                Email = "<EMAIL>"
            };
            await _client.PostAsJsonAsync("/api/auth/register", registerRequest);

            // 準備登入請求
            var loginRequest = new LoginRequest
            {
                Username = "logintest",
                Password = "password123"
            };

            // Act
            var response = await _client.PostAsJsonAsync("/api/auth/login", loginRequest);

            // Assert
            response.EnsureSuccessStatusCode();
            var content = await response.Content.ReadAsStringAsync();
            var authResponse = JsonSerializer.Deserialize<AuthResponse>(content, new JsonSerializerOptions { PropertyNameCaseInsensitive = true });
            
            Assert.NotNull(authResponse);
            Assert.True(authResponse.Success);
            Assert.NotNull(authResponse.Token);
            Assert.NotNull(authResponse.User);
            Assert.Equal("logintest", authResponse.User.Username);
        }

        [Fact]
        public async Task Login_WithInvalidCredentials_ReturnsUnauthorized()
        {
            // Arrange
            await CleanupTestData();
            var loginRequest = new LoginRequest
            {
                Username = "nonexistent",
                Password = "wrongpassword"
            };

            // Act
            var response = await _client.PostAsJsonAsync("/api/auth/login", loginRequest);

            // Assert
            Assert.Equal(System.Net.HttpStatusCode.Unauthorized, response.StatusCode);
            var content = await response.Content.ReadAsStringAsync();
            var authResponse = JsonSerializer.Deserialize<AuthResponse>(content, new JsonSerializerOptions { PropertyNameCaseInsensitive = true });
            
            Assert.NotNull(authResponse);
            Assert.False(authResponse.Success);
            Assert.Contains("用戶名稱或密碼錯誤", authResponse.Message);
        }

        [Fact]
        public async Task GetCurrentUser_WithValidToken_ReturnsUserInfo()
        {
            // Arrange
            await CleanupTestData();
            
            // 註冊並登入獲取權杖
            var registerRequest = new RegisterRequest
            {
                Username = "currentusertest",
                Password = "password123",
                ConfirmPassword = "password123",
                Email = "<EMAIL>"
            };
            var registerResponse = await _client.PostAsJsonAsync("/api/auth/register", registerRequest);
            var registerContent = await registerResponse.Content.ReadAsStringAsync();
            var authResponse = JsonSerializer.Deserialize<AuthResponse>(registerContent, new JsonSerializerOptions { PropertyNameCaseInsensitive = true });

            // 設定授權標頭
            _client.DefaultRequestHeaders.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", authResponse!.Token);

            // Act
            var response = await _client.GetAsync("/api/auth/me");

            // Assert
            response.EnsureSuccessStatusCode();
            var content = await response.Content.ReadAsStringAsync();
            var userDto = JsonSerializer.Deserialize<UserDto>(content, new JsonSerializerOptions { PropertyNameCaseInsensitive = true });
            
            Assert.NotNull(userDto);
            Assert.Equal("currentusertest", userDto.Username);
            Assert.Equal("<EMAIL>", userDto.Email);
        }

        [Fact]
        public async Task GetCurrentUser_WithoutToken_ReturnsUnauthorized()
        {
            // Arrange
            _client.DefaultRequestHeaders.Authorization = null;

            // Act
            var response = await _client.GetAsync("/api/auth/me");

            // Assert
            Assert.Equal(System.Net.HttpStatusCode.Unauthorized, response.StatusCode);
        }

        [Fact]
        public async Task GetCurrentUser_WithInvalidToken_ReturnsUnauthorized()
        {
            // Arrange
            _client.DefaultRequestHeaders.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", "invalid.jwt.token");

            // Act
            var response = await _client.GetAsync("/api/auth/me");

            // Assert
            Assert.Equal(System.Net.HttpStatusCode.Unauthorized, response.StatusCode);
        }

        [Fact]
        public async Task ChangePassword_WithValidData_ReturnsSuccess()
        {
            // Arrange
            await CleanupTestData();
            
            // 註冊並登入
            var registerRequest = new RegisterRequest
            {
                Username = "passwordchangetest",
                Password = "oldpassword123",
                ConfirmPassword = "oldpassword123",
                Email = "<EMAIL>"
            };
            var registerResponse = await _client.PostAsJsonAsync("/api/auth/register", registerRequest);
            var registerContent = await registerResponse.Content.ReadAsStringAsync();
            var authResponse = JsonSerializer.Deserialize<AuthResponse>(registerContent, new JsonSerializerOptions { PropertyNameCaseInsensitive = true });

            _client.DefaultRequestHeaders.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", authResponse!.Token);

            var changePasswordRequest = new ChangePasswordRequest
            {
                CurrentPassword = "oldpassword123",
                NewPassword = "newpassword123",
                ConfirmNewPassword = "newpassword123"
            };

            // Act
            var response = await _client.PostAsJsonAsync("/api/auth/change-password", changePasswordRequest);

            // Assert
            response.EnsureSuccessStatusCode();
            var content = await response.Content.ReadAsStringAsync();
            var changeResponse = JsonSerializer.Deserialize<AuthResponse>(content, new JsonSerializerOptions { PropertyNameCaseInsensitive = true });
            
            Assert.NotNull(changeResponse);
            Assert.True(changeResponse.Success);

            // 驗證新密碼可以登入
            _client.DefaultRequestHeaders.Authorization = null;
            var loginRequest = new LoginRequest
            {
                Username = "passwordchangetest",
                Password = "newpassword123"
            };
            var loginResponse = await _client.PostAsJsonAsync("/api/auth/login", loginRequest);
            loginResponse.EnsureSuccessStatusCode();
        }

        [Fact]
        public async Task ValidateToken_WithValidToken_ReturnsUserInfo()
        {
            // Arrange
            await CleanupTestData();
            
            // 註冊並獲取權杖
            var registerRequest = new RegisterRequest
            {
                Username = "validatetest",
                Password = "password123",
                ConfirmPassword = "password123",
                Email = "<EMAIL>"
            };
            var registerResponse = await _client.PostAsJsonAsync("/api/auth/register", registerRequest);
            var registerContent = await registerResponse.Content.ReadAsStringAsync();
            var authResponse = JsonSerializer.Deserialize<AuthResponse>(registerContent, new JsonSerializerOptions { PropertyNameCaseInsensitive = true });

            var validateRequest = new { Token = authResponse!.Token };

            // Act
            var response = await _client.PostAsJsonAsync("/api/auth/validate", validateRequest);

            // Assert
            response.EnsureSuccessStatusCode();
            var content = await response.Content.ReadAsStringAsync();
            var userDto = JsonSerializer.Deserialize<UserDto>(content, new JsonSerializerOptions { PropertyNameCaseInsensitive = true });
            
            Assert.NotNull(userDto);
            Assert.Equal("validatetest", userDto.Username);
        }

        private async Task CleanupTestData()
        {
            using var scope = _factory.Services.CreateScope();
            var context = scope.ServiceProvider.GetRequiredService<Net9QueryStockServerContext>();
            
            context.Users.RemoveRange(context.Users);
            await context.SaveChangesAsync();
        }
    }
}
