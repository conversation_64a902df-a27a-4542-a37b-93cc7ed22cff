[{"SourcePath": "C:\\Users\\<USER>\\vsdbg\\vs2017u5", "TargetPath": "/remote_debugger", "ReadOnly": false}, {"SourcePath": "C:\\Users\\<USER>\\AppData\\Roaming\\Microsoft\\UserSecrets", "TargetPath": "/root/.microsoft/usersecrets", "ReadOnly": true}, {"SourcePath": "C:\\Users\\<USER>\\AppData\\Roaming\\Microsoft\\UserSecrets", "TargetPath": "/home/<USER>/.microsoft/usersecrets", "ReadOnly": true}, {"SourcePath": "C:\\Users\\<USER>\\AppData\\Roaming\\ASP.NET\\Https", "TargetPath": "/root/.aspnet/https", "ReadOnly": true}, {"SourcePath": "C:\\Users\\<USER>\\AppData\\Roaming\\ASP.NET\\Https", "TargetPath": "/home/<USER>/.aspnet/https", "ReadOnly": true}, {"SourcePath": "C:\\Program Files\\Microsoft Visual Studio\\2022\\Enterprise\\MSBuild\\Sdks\\Microsoft.Docker.Sdk\\tools\\linux-x64\\net6.0", "TargetPath": "/VSTools", "ReadOnly": true}, {"SourcePath": "C:\\Program Files\\Microsoft Visual Studio\\2022\\Enterprise\\Common7\\IDE\\CommonExtensions\\Microsoft\\HotReload", "TargetPath": "/HotReloadAgent", "ReadOnly": true}, {"SourcePath": "C:\\Users\\<USER>\\source\\repos\\Net9.QueryStock\\Net9.QueryStock.Server", "TargetPath": "/app", "ReadOnly": false}, {"SourcePath": "C:\\Users\\<USER>\\source\\repos\\Net9.QueryStock", "TargetPath": "/src/", "ReadOnly": false}, {"SourcePath": "C:\\Users\\<USER>\\.nuget\\packages", "TargetPath": "/.nuget/fallbackpackages2", "ReadOnly": false}, {"SourcePath": "C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages", "TargetPath": "/.nuget/fallbackpackages", "ReadOnly": false}]