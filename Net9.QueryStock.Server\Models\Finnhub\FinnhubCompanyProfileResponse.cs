using System.Text.Json.Serialization;

namespace Net9.QueryStock.Server.Models.Finnhub
{
    /// <summary>
    /// Finnhub 公司資料回應模型
    /// </summary>
    public class FinnhubCompanyProfileResponse
    {
        /// <summary>
        /// 國家
        /// </summary>
        [JsonPropertyName("country")]
        public string? Country { get; set; }
        
        /// <summary>
        /// 貨幣
        /// </summary>
        [JsonPropertyName("currency")]
        public string? Currency { get; set; }
        
        /// <summary>
        /// 交易所
        /// </summary>
        [JsonPropertyName("exchange")]
        public string? Exchange { get; set; }
        
        /// <summary>
        /// 行業分類
        /// </summary>
        [JsonPropertyName("finnhubIndustry")]
        public string? Industry { get; set; }
        
        /// <summary>
        /// 上市日期
        /// </summary>
        [JsonPropertyName("ipo")]
        public string? IpoDate { get; set; }
        
        /// <summary>
        /// 公司標誌 URL
        /// </summary>
        [JsonPropertyName("logo")]
        public string? Logo { get; set; }
        
        /// <summary>
        /// 市值
        /// </summary>
        [JsonPropertyName("marketCapitalization")]
        public decimal? MarketCapitalization { get; set; }
        
        /// <summary>
        /// 公司名稱
        /// </summary>
        [JsonPropertyName("name")]
        public string? Name { get; set; }
        
        /// <summary>
        /// 電話
        /// </summary>
        [JsonPropertyName("phone")]
        public string? Phone { get; set; }
        
        /// <summary>
        /// 流通股數
        /// </summary>
        [JsonPropertyName("shareOutstanding")]
        public decimal? SharesOutstanding { get; set; }
        
        /// <summary>
        /// 股票代號
        /// </summary>
        [JsonPropertyName("ticker")]
        public string? Ticker { get; set; }
        
        /// <summary>
        /// 網站
        /// </summary>
        [JsonPropertyName("weburl")]
        public string? Website { get; set; }
    }
}
