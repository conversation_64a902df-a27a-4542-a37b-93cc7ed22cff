<template>
  <div class="layout">
    <!-- 頂部導航欄 -->
    <header class="header">
      <div class="container">
        <div class="header-content">
          <!-- Logo 和品牌 -->
          <div class="brand">
            <router-link to="/" class="brand-link">
              <div class="logo">📈</div>
              <div class="brand-text">
                <h1>股票查詢平台</h1>
                <span class="tagline">即時股票資訊查詢系統</span>
              </div>
            </router-link>
          </div>
          
          <!-- 主導航 -->
          <nav class="main-nav">
            <router-link to="/" class="nav-item">首頁</router-link>
            <router-link to="/setup" class="nav-item">系統設定</router-link>
            <template v-if="isLoggedIn">
              <router-link to="/dashboard" class="nav-item">儀表板</router-link>
              <router-link to="/keys" class="nav-item">金鑰管理</router-link>
            </template>
          </nav>
          
          <!-- 用戶操作區 -->
          <div class="user-actions">
            <template v-if="isLoggedIn">
              <div class="user-info">
                <div class="user-avatar">{{ userInitials }}</div>
                <span class="user-name">{{ userName }}</span>
              </div>
              <div class="dropdown">
                <button class="dropdown-toggle" @click="showUserMenu = !showUserMenu">
                  <span class="dropdown-icon">▼</span>
                </button>
                <div v-if="showUserMenu" class="dropdown-menu" @click="showUserMenu = false">
                  <router-link to="/profile" class="dropdown-item">個人資料</router-link>
                  <button @click="handleLogout" class="dropdown-item logout-btn">登出</button>
                </div>
              </div>
            </template>
            <template v-else>
              <router-link to="/login" class="btn btn-outline">登入</router-link>
              <router-link to="/register" class="btn btn-primary">註冊</router-link>
            </template>
          </div>
        </div>
      </div>
    </header>

    <!-- 主要內容區 -->
    <main class="main">
      <div class="container">
        <slot />
      </div>
    </main>

    <!-- 頁腳 -->
    <footer class="footer">
      <div class="container">
        <div class="footer-content">
          <div class="footer-section">
            <h3>股票查詢平台</h3>
            <p>提供即時、準確的股票資訊查詢服務</p>
          </div>
          <div class="footer-section">
            <h4>功能</h4>
            <ul>
              <li><router-link to="/">股票查詢</router-link></li>
              <li><router-link to="/setup">系統設定</router-link></li>
              <li><router-link to="/dashboard" v-if="isLoggedIn">儀表板</router-link></li>
            </ul>
          </div>
          <div class="footer-section">
            <h4>支援</h4>
            <ul>
              <li><a href="https://finnhub.io" target="_blank">Finnhub API</a></li>
              <li><router-link to="/setup">設定指南</router-link></li>
            </ul>
          </div>
          <div class="footer-section">
            <h4>關於</h4>
            <p>基於 .NET 9 和 Vue 3 構建的現代化股票查詢平台</p>
          </div>
        </div>
        <div class="footer-bottom">
          <p>&copy; 2024 股票查詢平台. 保留所有權利.</p>
        </div>
      </div>
    </footer>
  </div>
</template>

<script>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '../stores/auth'

export default {
  name: 'Layout',
  setup() {
    const router = useRouter()
    const authStore = useAuthStore()
    const showUserMenu = ref(false)
    
    const isLoggedIn = computed(() => authStore.isLoggedIn)
    const userName = computed(() => authStore.userName)
    const userInitials = computed(() => {
      if (!userName.value) return 'U'
      return userName.value.substring(0, 2).toUpperCase()
    })
    
    const handleLogout = () => {
      authStore.logout()
      showUserMenu.value = false
      router.push('/')
    }
    
    const handleClickOutside = (event) => {
      if (!event.target.closest('.dropdown')) {
        showUserMenu.value = false
      }
    }
    
    onMounted(() => {
      document.addEventListener('click', handleClickOutside)
    })
    
    onUnmounted(() => {
      document.removeEventListener('click', handleClickOutside)
    })
    
    return {
      showUserMenu,
      isLoggedIn,
      userName,
      userInitials,
      handleLogout
    }
  }
}
</script>

<style scoped>
.layout {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

/* 頭部樣式 */
.header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  position: sticky;
  top: 0;
  z-index: 100;
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem 0;
  gap: 2rem;
}

.brand-link {
  display: flex;
  align-items: center;
  gap: 1rem;
  text-decoration: none;
  color: inherit;
}

.logo {
  font-size: 2.5rem;
}

.brand-text h1 {
  margin: 0;
  font-size: 1.5rem;
  font-weight: 600;
}

.tagline {
  font-size: 0.9rem;
  opacity: 0.9;
}

.main-nav {
  display: flex;
  gap: 2rem;
  flex: 1;
  justify-content: center;
}

.nav-item {
  color: white;
  text-decoration: none;
  padding: 0.5rem 1rem;
  border-radius: 6px;
  transition: background-color 0.3s ease;
  font-weight: 500;
}

.nav-item:hover,
.nav-item.router-link-active {
  background-color: rgba(255, 255, 255, 0.2);
}

.user-actions {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.user-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 0.9rem;
}

.user-name {
  font-weight: 500;
}

.dropdown {
  position: relative;
}

.dropdown-toggle {
  background: none;
  border: none;
  color: white;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 4px;
  transition: background-color 0.3s ease;
}

.dropdown-toggle:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.dropdown-menu {
  position: absolute;
  top: 100%;
  right: 0;
  background: white;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  min-width: 150px;
  overflow: hidden;
  z-index: 1000;
}

.dropdown-item {
  display: block;
  width: 100%;
  padding: 0.75rem 1rem;
  text-decoration: none;
  color: #333;
  border: none;
  background: none;
  text-align: left;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.dropdown-item:hover {
  background-color: #f8f9fa;
}

.btn {
  padding: 0.5rem 1rem;
  border-radius: 6px;
  text-decoration: none;
  font-weight: 500;
  transition: all 0.3s ease;
  border: 2px solid transparent;
  cursor: pointer;
}

.btn-primary {
  background: white;
  color: #667eea;
}

.btn-primary:hover {
  background: #f8f9fa;
  transform: translateY(-1px);
}

.btn-outline {
  background: transparent;
  color: white;
  border-color: rgba(255, 255, 255, 0.3);
}

.btn-outline:hover {
  background: rgba(255, 255, 255, 0.1);
  border-color: rgba(255, 255, 255, 0.5);
}

/* 主要內容區 */
.main {
  flex: 1;
  padding: 2rem 0;
  background-color: #f8f9fa;
}

/* 頁腳樣式 */
.footer {
  background: #2c3e50;
  color: white;
  padding: 3rem 0 1rem;
}

.footer-content {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
  margin-bottom: 2rem;
}

.footer-section h3,
.footer-section h4 {
  margin: 0 0 1rem 0;
  color: #ecf0f1;
}

.footer-section p {
  margin: 0;
  color: #bdc3c7;
  line-height: 1.6;
}

.footer-section ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.footer-section li {
  margin-bottom: 0.5rem;
}

.footer-section a {
  color: #bdc3c7;
  text-decoration: none;
  transition: color 0.3s ease;
}

.footer-section a:hover {
  color: #ecf0f1;
}

.footer-bottom {
  border-top: 1px solid #34495e;
  padding-top: 1rem;
  text-align: center;
}

.footer-bottom p {
  margin: 0;
  color: #95a5a6;
}

/* 響應式設計 */
@media (max-width: 768px) {
  .header-content {
    flex-direction: column;
    gap: 1rem;
  }
  
  .brand-link {
    flex-direction: column;
    text-align: center;
    gap: 0.5rem;
  }
  
  .main-nav {
    order: 3;
    width: 100%;
    justify-content: center;
    flex-wrap: wrap;
    gap: 1rem;
  }
  
  .user-actions {
    order: 2;
  }
  
  .main {
    padding: 1rem 0;
  }
  
  .footer-content {
    grid-template-columns: 1fr;
    text-align: center;
  }
}

@media (max-width: 480px) {
  .container {
    padding: 0 0.5rem;
  }
  
  .main-nav {
    gap: 0.5rem;
  }
  
  .nav-item {
    padding: 0.5rem;
    font-size: 0.9rem;
  }
}
</style>
