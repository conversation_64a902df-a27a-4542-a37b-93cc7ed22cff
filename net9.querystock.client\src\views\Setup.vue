<template>
  <div class="setup">
    <!-- 導航欄 -->
    <nav class="navbar">
      <div class="nav-brand">
        <h1>股票查詢平台</h1>
      </div>
      <div class="nav-menu">
        <router-link to="/" class="nav-link">首頁</router-link>
        <router-link to="/dashboard" class="nav-link" v-if="isLoggedIn">儀表板</router-link>
        <router-link to="/keys" class="nav-link" v-if="isLoggedIn">金鑰管理</router-link>
        <router-link to="/profile" class="nav-link" v-if="isLoggedIn">個人資料</router-link>
        <template v-if="isLoggedIn">
          <button @click="handleLogout" class="nav-link logout-btn">登出</button>
        </template>
        <template v-else>
          <router-link to="/login" class="nav-link">登入</router-link>
          <router-link to="/register" class="nav-link">註冊</router-link>
        </template>
      </div>
    </nav>

    <div class="content">
      <!-- 系統狀態 -->
      <div class="status-card" :class="systemStatus?.systemReady ? 'ready' : 'not-ready'">
        <div class="status-header">
          <div class="status-icon">
            {{ systemStatus?.systemReady ? '✅' : '⚠️' }}
          </div>
          <div class="status-info">
            <h3>{{ systemStatus?.systemReady ? '系統已就緒' : '需要設定' }}</h3>
            <p>{{ systemStatus?.message }}</p>
          </div>
        </div>
        <button @click="checkSystemStatus" class="btn btn-secondary" :disabled="loading">
          {{ loading ? '檢查中...' : '重新檢查' }}
        </button>
      </div>

      <!-- 設定指南 -->
      <div class="setup-guide">
        <h2>🚀 快速設定指南</h2>
        <p>按照以下步驟設定您的股票查詢平台：</p>
        
        <div class="steps">
          <div v-for="step in setupSteps" :key="step.step" class="step-card">
            <div class="step-number">{{ step.step }}</div>
            <div class="step-content">
              <h4>{{ step.title }}</h4>
              <p>{{ step.description }}</p>
              <div v-if="step.details" class="step-details">
                <small>{{ step.details }}</small>
              </div>
              <div v-if="step.endpoint" class="step-code">
                <strong>API 端點：</strong> {{ step.endpoint }}
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 快速設定表單 -->
      <div class="quick-setup">
        <h3>🔑 快速添加 Finnhub API 金鑰</h3>
        <p>如果您已經有 Finnhub API 金鑰，可以在這裡快速添加：</p>
        
        <form @submit.prevent="setupFinnhubKey" class="setup-form">
          <div class="form-group">
            <label for="keyName">金鑰名稱（可選）</label>
            <input
              id="keyName"
              v-model="setupForm.keyName"
              type="text"
              placeholder="例如: My-Finnhub-Key"
              :disabled="submitting"
            />
          </div>
          
          <div class="form-group">
            <label for="apiKey">Finnhub API 金鑰 *</label>
            <input
              id="apiKey"
              v-model="setupForm.apiKey"
              type="password"
              placeholder="請輸入您的 Finnhub API 金鑰"
              required
              :disabled="submitting"
            />
            <small class="help-text">
              您可以在 <a href="https://finnhub.io/dashboard" target="_blank">Finnhub Dashboard</a> 找到您的 API 金鑰
            </small>
          </div>
          
          <div v-if="setupError" class="error-message">
            {{ setupError }}
          </div>
          
          <div v-if="setupSuccess" class="success-message">
            {{ setupSuccess }}
          </div>
          
          <button type="submit" class="btn btn-primary" :disabled="submitting">
            {{ submitting ? '設定中...' : '添加 API 金鑰' }}
          </button>
        </form>
      </div>

      <!-- 提供者狀態 -->
      <div v-if="systemStatus?.providers" class="providers-status">
        <h3>📊 服務提供者狀態</h3>
        <div class="providers-grid">
          <div v-for="provider in systemStatus.providers" :key="provider.name" class="provider-card">
            <div class="provider-header">
              <h4>{{ provider.name }}</h4>
              <span class="provider-status" :class="provider.isReady ? 'ready' : 'not-ready'">
                {{ provider.isReady ? '已就緒' : '需要金鑰' }}
              </span>
            </div>
            <div class="provider-stats">
              <div class="stat-item">
                <span class="label">總金鑰數:</span>
                <span class="value">{{ provider.totalKeys }}</span>
              </div>
              <div class="stat-item">
                <span class="label">可用金鑰:</span>
                <span class="value">{{ provider.availableKeys }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 有用的連結 -->
      <div class="useful-links">
        <h3>🔗 有用的連結</h3>
        <div class="links-grid">
          <a href="https://finnhub.io" target="_blank" class="link-card">
            <div class="link-icon">🌐</div>
            <div class="link-content">
              <h4>Finnhub.io</h4>
              <p>註冊免費帳號並獲取 API 金鑰</p>
            </div>
          </a>
          
          <router-link to="/keys" class="link-card" v-if="isLoggedIn">
            <div class="link-icon">🔑</div>
            <div class="link-content">
              <h4>金鑰管理</h4>
              <p>管理您的 API 金鑰和查看使用統計</p>
            </div>
          </router-link>
          
          <router-link to="/dashboard" class="link-card" v-if="isLoggedIn">
            <div class="link-icon">📊</div>
            <div class="link-content">
              <h4>儀表板</h4>
              <p>查看您的使用統計和系統狀態</p>
            </div>
          </router-link>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '../stores/auth'
import axios from 'axios'

export default {
  name: 'Setup',
  setup() {
    const router = useRouter()
    const authStore = useAuthStore()
    
    const loading = ref(false)
    const submitting = ref(false)
    const setupError = ref('')
    const setupSuccess = ref('')
    const systemStatus = ref(null)
    const setupSteps = ref([])
    
    const setupForm = ref({
      keyName: '',
      apiKey: ''
    })
    
    const isLoggedIn = computed(() => authStore.isLoggedIn)
    
    const handleLogout = () => {
      authStore.logout()
      router.push('/')
    }
    
    const checkSystemStatus = async () => {
      loading.value = true
      try {
        const response = await axios.get('/api/admin/status')
        systemStatus.value = response.data
      } catch (error) {
        console.error('檢查系統狀態失敗:', error)
      } finally {
        loading.value = false
      }
    }
    
    const loadSetupGuide = async () => {
      try {
        const response = await axios.get('/api/admin/setup-guide')
        setupSteps.value = response.data.steps
      } catch (error) {
        console.error('載入設定指南失敗:', error)
        // 提供預設的設定步驟
        setupSteps.value = [
          {
            step: 1,
            title: "註冊 Finnhub 帳號",
            description: "前往 https://finnhub.io 註冊免費帳號",
            details: "免費帳號每分鐘可查詢 60 次，每月 1000 次"
          },
          {
            step: 2,
            title: "獲取 API 金鑰",
            description: "登入 Finnhub 後，在 Dashboard 頁面複製您的 API 金鑰",
            details: "API 金鑰格式類似：c123456789abcdef"
          },
          {
            step: 3,
            title: "添加 API 金鑰",
            description: "使用下方的表單添加您的 API 金鑰到系統中"
          },
          {
            step: 4,
            title: "開始使用",
            description: "設定完成後即可開始使用股票查詢功能"
          }
        ]
      }
    }
    
    const setupFinnhubKey = async () => {
      if (!setupForm.value.apiKey.trim()) {
        setupError.value = 'API 金鑰不能為空'
        return
      }
      
      submitting.value = true
      setupError.value = ''
      setupSuccess.value = ''
      
      try {
        const response = await axios.post('/api/admin/setup/finnhub-key', {
          keyName: setupForm.value.keyName || undefined,
          apiKey: setupForm.value.apiKey
        })
        
        setupSuccess.value = response.data.message
        setupForm.value = { keyName: '', apiKey: '' }
        
        // 重新檢查系統狀態
        await checkSystemStatus()
        
      } catch (error) {
        setupError.value = error.response?.data?.message || '設定失敗，請稍後再試'
      } finally {
        submitting.value = false
      }
    }
    
    onMounted(() => {
      checkSystemStatus()
      loadSetupGuide()
    })
    
    return {
      loading,
      submitting,
      setupError,
      setupSuccess,
      systemStatus,
      setupSteps,
      setupForm,
      isLoggedIn,
      handleLogout,
      checkSystemStatus,
      setupFinnhubKey
    }
  }
}
</script>

<style scoped>
.setup {
  min-height: 100vh;
  background-color: #f8f9fa;
}

.navbar {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 1rem 2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.nav-brand h1 {
  margin: 0;
  font-size: 1.5rem;
  font-weight: 600;
}

.nav-menu {
  display: flex;
  gap: 1rem;
  align-items: center;
}

.nav-link {
  color: white;
  text-decoration: none;
  padding: 0.5rem 1rem;
  border-radius: 6px;
  transition: background-color 0.3s ease;
  font-weight: 500;
}

.nav-link:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.logout-btn {
  background: none;
  border: none;
  cursor: pointer;
  font-size: inherit;
  font-family: inherit;
}

.content {
  max-width: 1000px;
  margin: 0 auto;
  padding: 2rem;
}

.status-card {
  background: white;
  padding: 2rem;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  margin-bottom: 2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.status-card.ready {
  border-left: 4px solid #28a745;
}

.status-card.not-ready {
  border-left: 4px solid #ffc107;
}

.status-header {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.status-icon {
  font-size: 2rem;
}

.status-info h3 {
  margin: 0 0 0.5rem 0;
  color: #333;
}

.status-info p {
  margin: 0;
  color: #666;
}

.setup-guide,
.quick-setup,
.providers-status,
.useful-links {
  background: white;
  padding: 2rem;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  margin-bottom: 2rem;
}

.setup-guide h2,
.quick-setup h3,
.providers-status h3,
.useful-links h3 {
  margin: 0 0 1.5rem 0;
  color: #333;
}

.steps {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.step-card {
  display: flex;
  gap: 1rem;
  padding: 1.5rem;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  background: #f8f9fa;
}

.step-number {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  flex-shrink: 0;
}

.step-content h4 {
  margin: 0 0 0.5rem 0;
  color: #333;
}

.step-content p {
  margin: 0 0 0.5rem 0;
  color: #666;
}

.step-details {
  color: #888;
  font-style: italic;
}

.step-code {
  margin-top: 0.5rem;
  font-family: monospace;
  background: #e9ecef;
  padding: 0.5rem;
  border-radius: 4px;
  font-size: 0.9rem;
}

.setup-form {
  max-width: 500px;
}

.form-group {
  margin-bottom: 1rem;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: #333;
}

.form-group input {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 1rem;
  box-sizing: border-box;
}

.form-group input:focus {
  outline: none;
  border-color: #667eea;
}

.help-text {
  display: block;
  margin-top: 0.25rem;
  color: #666;
  font-size: 0.9rem;
}

.help-text a {
  color: #667eea;
  text-decoration: none;
}

.help-text a:hover {
  text-decoration: underline;
}

.error-message {
  background-color: #fee;
  color: #c33;
  padding: 0.75rem;
  border-radius: 6px;
  margin-bottom: 1rem;
  border: 1px solid #fcc;
}

.success-message {
  background-color: #efe;
  color: #3c3;
  padding: 0.75rem;
  border-radius: 6px;
  margin-bottom: 1rem;
  border: 1px solid #cfc;
}

.btn {
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.btn-primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.btn-secondary {
  background: #6c757d;
  color: white;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.providers-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1rem;
}

.provider-card {
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 1rem;
  background: #f8f9fa;
}

.provider-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.provider-header h4 {
  margin: 0;
  color: #333;
}

.provider-status {
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
  text-transform: uppercase;
}

.provider-status.ready {
  background: #d4edda;
  color: #155724;
}

.provider-status.not-ready {
  background: #fff3cd;
  color: #856404;
}

.provider-stats {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.stat-item {
  display: flex;
  justify-content: space-between;
}

.label {
  color: #666;
}

.value {
  font-weight: 600;
  color: #333;
}

.links-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1rem;
}

.link-card {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  text-decoration: none;
  color: inherit;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.link-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  text-decoration: none;
  color: inherit;
}

.link-icon {
  font-size: 2rem;
}

.link-content h4 {
  margin: 0 0 0.25rem 0;
  color: #333;
}

.link-content p {
  margin: 0;
  color: #666;
  font-size: 0.9rem;
}

@media (max-width: 768px) {
  .nav-menu {
    flex-direction: column;
    gap: 0.5rem;
  }
  
  .content {
    padding: 1rem;
  }
  
  .status-card {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }
  
  .step-card {
    flex-direction: column;
  }
  
  .providers-grid,
  .links-grid {
    grid-template-columns: 1fr;
  }
}
</style>
