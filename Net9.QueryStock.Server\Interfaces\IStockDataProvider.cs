using Net9.QueryStock.Server.DTOs;

namespace Net9.QueryStock.Server.Interfaces
{
    /// <summary>
    /// 股票資料提供者介面
    /// </summary>
    public interface IStockDataProvider
    {
        /// <summary>
        /// 提供者名稱
        /// </summary>
        string ProviderName { get; }
        
        /// <summary>
        /// 是否可用
        /// </summary>
        bool IsAvailable { get; }
        
        /// <summary>
        /// 獲取即時股票報價
        /// </summary>
        /// <param name="symbol">股票代號</param>
        /// <param name="cancellationToken">取消權杖</param>
        /// <returns>股票報價資訊</returns>
        Task<StockQuoteDto?> GetRealTimeQuoteAsync(string symbol, CancellationToken cancellationToken = default);
        
        /// <summary>
        /// 獲取股票詳細資訊
        /// </summary>
        /// <param name="symbol">股票代號</param>
        /// <param name="cancellationToken">取消權杖</param>
        /// <returns>股票詳細資訊</returns>
        Task<StockInfoDto?> GetStockInfoAsync(string symbol, CancellationToken cancellationToken = default);
        
        /// <summary>
        /// 獲取歷史價格資料
        /// </summary>
        /// <param name="symbol">股票代號</param>
        /// <param name="fromDate">開始日期</param>
        /// <param name="toDate">結束日期</param>
        /// <param name="cancellationToken">取消權杖</param>
        /// <returns>歷史價格資料列表</returns>
        Task<IEnumerable<StockPriceDto>> GetHistoricalPricesAsync(
            string symbol, 
            DateOnly fromDate, 
            DateOnly toDate, 
            CancellationToken cancellationToken = default);
        
        /// <summary>
        /// 搜尋股票
        /// </summary>
        /// <param name="query">搜尋關鍵字</param>
        /// <param name="cancellationToken">取消權杖</param>
        /// <returns>符合條件的股票列表</returns>
        Task<IEnumerable<StockInfoDto>> SearchStocksAsync(string query, CancellationToken cancellationToken = default);
        
        /// <summary>
        /// 批量獲取即時報價
        /// </summary>
        /// <param name="symbols">股票代號列表</param>
        /// <param name="cancellationToken">取消權杖</param>
        /// <returns>股票報價字典</returns>
        Task<Dictionary<string, StockQuoteDto>> GetBatchQuotesAsync(
            IEnumerable<string> symbols, 
            CancellationToken cancellationToken = default);
    }
}
