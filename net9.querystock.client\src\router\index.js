import { createRouter, createWebHistory } from 'vue-router'
import { useAuthStore } from '../stores/auth'

// 頁面組件
import Home from '../views/Home.vue'
import Login from '../views/Login.vue'
import Register from '../views/Register.vue'
import Dashboard from '../views/Dashboard.vue'
import KeyManagement from '../views/KeyManagement.vue'
import Profile from '../views/Profile.vue'
import Setup from '../views/Setup.vue'

const routes = [
  {
    path: '/',
    name: 'Home',
    component: Home,
    meta: { requiresAuth: false }
  },
  {
    path: '/login',
    name: 'Login',
    component: Login,
    meta: { requiresAuth: false, hideForAuth: true }
  },
  {
    path: '/register',
    name: 'Register',
    component: Register,
    meta: { requiresAuth: false, hideForAuth: true }
  },
  {
    path: '/dashboard',
    name: 'Dashboard',
    component: Dashboard,
    meta: { requiresAuth: true }
  },
  {
    path: '/keys',
    name: 'KeyManagement',
    component: KeyManagement,
    meta: { requiresAuth: true }
  },
  {
    path: '/profile',
    name: 'Profile',
    component: Profile,
    meta: { requiresAuth: true }
  },
  {
    path: '/setup',
    name: 'Setup',
    component: Setup,
    meta: { requiresAuth: false }
  },
  {
    path: '/:pathMatch(.*)*',
    redirect: '/'
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

// 路由守衛
router.beforeEach(async (to, from, next) => {
  const authStore = useAuthStore()
  
  // 初始化認證狀態（僅在第一次訪問時）
  if (!authStore.user && authStore.token) {
    try {
      await authStore.initAuth()
    } catch (error) {
      console.error('認證初始化失敗:', error)
    }
  }
  
  const requiresAuth = to.matched.some(record => record.meta.requiresAuth)
  const hideForAuth = to.matched.some(record => record.meta.hideForAuth)
  const isAuthenticated = authStore.isLoggedIn
  
  if (requiresAuth && !isAuthenticated) {
    // 需要認證但未登入，重定向到登入頁面
    next({
      name: 'Login',
      query: { redirect: to.fullPath }
    })
  } else if (hideForAuth && isAuthenticated) {
    // 已登入用戶訪問登入/註冊頁面，重定向到儀表板
    next({ name: 'Dashboard' })
  } else {
    next()
  }
})

export default router
