using System.Text.Json.Serialization;

namespace Net9.QueryStock.Server.Models.Finnhub
{
    /// <summary>
    /// Finnhub K線資料回應模型
    /// </summary>
    public class FinnhubCandleResponse
    {
        /// <summary>
        /// 收盤價陣列
        /// </summary>
        [JsonPropertyName("c")]
        public decimal[]? ClosePrices { get; set; }
        
        /// <summary>
        /// 最高價陣列
        /// </summary>
        [JsonPropertyName("h")]
        public decimal[]? HighPrices { get; set; }
        
        /// <summary>
        /// 最低價陣列
        /// </summary>
        [JsonPropertyName("l")]
        public decimal[]? LowPrices { get; set; }
        
        /// <summary>
        /// 開盤價陣列
        /// </summary>
        [JsonPropertyName("o")]
        public decimal[]? OpenPrices { get; set; }
        
        /// <summary>
        /// 狀態
        /// </summary>
        [JsonPropertyName("s")]
        public string? Status { get; set; }
        
        /// <summary>
        /// 時間戳記陣列
        /// </summary>
        [JsonPropertyName("t")]
        public long[]? Timestamps { get; set; }
        
        /// <summary>
        /// 成交量陣列
        /// </summary>
        [JsonPropertyName("v")]
        public long[]? Volumes { get; set; }
    }
}
