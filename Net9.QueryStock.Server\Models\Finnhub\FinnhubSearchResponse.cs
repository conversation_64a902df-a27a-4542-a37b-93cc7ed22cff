using System.Text.Json.Serialization;

namespace Net9.QueryStock.Server.Models.Finnhub
{
    /// <summary>
    /// Finnhub 搜尋回應模型
    /// </summary>
    public class FinnhubSearchResponse
    {
        /// <summary>
        /// 搜尋結果數量
        /// </summary>
        [JsonPropertyName("count")]
        public int Count { get; set; }
        
        /// <summary>
        /// 搜尋結果列表
        /// </summary>
        [JsonPropertyName("result")]
        public List<FinnhubSearchResult> Results { get; set; } = new();
    }
    
    /// <summary>
    /// Finnhub 搜尋結果項目
    /// </summary>
    public class FinnhubSearchResult
    {
        /// <summary>
        /// 公司描述
        /// </summary>
        [JsonPropertyName("description")]
        public string? Description { get; set; }
        
        /// <summary>
        /// 顯示代號
        /// </summary>
        [JsonPropertyName("displaySymbol")]
        public string? DisplaySymbol { get; set; }
        
        /// <summary>
        /// 股票代號
        /// </summary>
        [JsonPropertyName("symbol")]
        public string? Symbol { get; set; }
        
        /// <summary>
        /// 股票類型
        /// </summary>
        [JsonPropertyName("type")]
        public string? Type { get; set; }
    }
}
