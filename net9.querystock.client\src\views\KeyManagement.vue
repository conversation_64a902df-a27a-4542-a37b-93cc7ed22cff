<template>
  <div class="key-management">
    <!-- 導航欄 -->
    <nav class="navbar">
      <div class="nav-brand">
        <h1>股票查詢平台</h1>
      </div>
      <div class="nav-menu">
        <router-link to="/" class="nav-link">首頁</router-link>
        <router-link to="/dashboard" class="nav-link">儀表板</router-link>
        <router-link to="/keys" class="nav-link">金鑰管理</router-link>
        <router-link to="/profile" class="nav-link">個人資料</router-link>
        <button @click="handleLogout" class="nav-link logout-btn">登出</button>
      </div>
    </nav>

    <div class="content">
      <div class="page-header">
        <h2>API 金鑰管理</h2>
        <p>管理您的 API 金鑰，監控使用情況和效能統計</p>
      </div>

      <!-- 統計卡片 -->
      <div class="stats-grid">
        <div class="stat-card">
          <div class="stat-icon">🔑</div>
          <div class="stat-content">
            <h3>總金鑰數</h3>
            <p class="stat-number">{{ totalKeys }}</p>
          </div>
        </div>
        
        <div class="stat-card">
          <div class="stat-icon">✅</div>
          <div class="stat-content">
            <h3>可用金鑰</h3>
            <p class="stat-number">{{ availableKeys }}</p>
          </div>
        </div>
        
        <div class="stat-card">
          <div class="stat-icon">📊</div>
          <div class="stat-content">
            <h3>今日使用</h3>
            <p class="stat-number">{{ todayUsage }}</p>
          </div>
        </div>
        
        <div class="stat-card">
          <div class="stat-icon">⚡</div>
          <div class="stat-content">
            <h3>成功率</h3>
            <p class="stat-number">{{ successRate }}%</p>
          </div>
        </div>
      </div>

      <!-- 操作按鈕 -->
      <div class="actions">
        <button @click="showAddKeyModal = true" class="btn btn-primary">
          <span class="btn-icon">➕</span>
          新增金鑰
        </button>
        <button @click="refreshData" class="btn btn-secondary" :disabled="loading">
          <span class="btn-icon">🔄</span>
          {{ loading ? '更新中...' : '刷新資料' }}
        </button>
      </div>

      <!-- 金鑰列表 -->
      <div class="key-list">
        <h3>金鑰列表</h3>
        <div v-if="loading" class="loading">載入中...</div>
        <div v-else-if="keys.length === 0" class="no-keys">
          <p>尚未新增任何金鑰</p>
          <button @click="showAddKeyModal = true" class="btn btn-primary">新增第一個金鑰</button>
        </div>
        <div v-else class="keys-grid">
          <div v-for="key in keys" :key="key.id" class="key-card">
            <div class="key-header">
              <h4>{{ key.name }}</h4>
              <span class="key-status" :class="key.status.toLowerCase()">
                {{ getStatusText(key.status) }}
              </span>
            </div>
            <div class="key-info">
              <div class="info-item">
                <span class="label">提供者:</span>
                <span class="value">{{ key.providerName || 'Finnhub' }}</span>
              </div>
              <div class="info-item">
                <span class="label">優先級:</span>
                <span class="value">{{ key.priority }}</span>
              </div>
              <div class="info-item">
                <span class="label">使用次數:</span>
                <span class="value">{{ key.totalUsageCount }}</span>
              </div>
              <div class="info-item">
                <span class="label">最後使用:</span>
                <span class="value">{{ formatDate(key.lastUsedAt) }}</span>
              </div>
            </div>
            <div class="key-actions">
              <button @click="editKey(key)" class="btn btn-sm btn-secondary">編輯</button>
              <button @click="toggleKeyStatus(key)" class="btn btn-sm" 
                      :class="key.status === 'Active' ? 'btn-warning' : 'btn-success'">
                {{ key.status === 'Active' ? '停用' : '啟用' }}
              </button>
              <button @click="deleteKey(key)" class="btn btn-sm btn-danger">刪除</button>
            </div>
          </div>
        </div>
      </div>

      <!-- 新增金鑰模態框 -->
      <div v-if="showAddKeyModal" class="modal-overlay" @click="closeModal">
        <div class="modal" @click.stop>
          <div class="modal-header">
            <h3>新增 API 金鑰</h3>
            <button @click="closeModal" class="close-btn">&times;</button>
          </div>
          <form @submit.prevent="addKey" class="modal-body">
            <div class="form-group">
              <label>金鑰名稱</label>
              <input v-model="newKey.name" type="text" required placeholder="例如: Finnhub-Key-1">
            </div>
            <div class="form-group">
              <label>API 金鑰</label>
              <input v-model="newKey.apiKey" type="password" required placeholder="請輸入您的 API 金鑰">
            </div>
            <div class="form-group">
              <label>服務提供者</label>
              <select v-model="newKey.provider" required>
                <option value="Finnhub">Finnhub</option>
                <option value="AlphaVantage">Alpha Vantage</option>
                <option value="YahooFinance">Yahoo Finance</option>
              </select>
            </div>
            <div class="form-group">
              <label>優先級</label>
              <input v-model.number="newKey.priority" type="number" min="1" max="10" value="1">
            </div>
            <div class="modal-footer">
              <button type="button" @click="closeModal" class="btn btn-secondary">取消</button>
              <button type="submit" class="btn btn-primary" :disabled="submitting">
                {{ submitting ? '新增中...' : '新增金鑰' }}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '../stores/auth'
import axios from 'axios'

export default {
  name: 'KeyManagement',
  setup() {
    const router = useRouter()
    const authStore = useAuthStore()
    
    const loading = ref(false)
    const submitting = ref(false)
    const showAddKeyModal = ref(false)
    const keys = ref([])
    const providers = ref([])
    
    const newKey = ref({
      name: '',
      apiKey: '',
      provider: 'Finnhub',
      priority: 1
    })
    
    const totalKeys = computed(() => keys.value.length)
    const availableKeys = computed(() => keys.value.filter(k => k.isAvailable).length)
    const todayUsage = computed(() => keys.value.reduce((sum, k) => sum + (k.todayUsageCount || 0), 0))
    const successRate = computed(() => {
      const total = keys.value.reduce((sum, k) => sum + (k.totalUsageCount || 0), 0)
      if (total === 0) return 100
      // 這裡可以根據實際的成功/失敗統計來計算
      return 95 // 暫時返回固定值
    })
    
    const handleLogout = () => {
      authStore.logout()
      router.push('/')
    }
    
    const refreshData = async () => {
      loading.value = true
      try {
        // 獲取所有提供者的金鑰
        const response = await axios.get('/api/apikeymanagement/providers')
        providers.value = response.data
        
        // 整理所有金鑰到一個列表中
        keys.value = []
        for (const provider of providers.value) {
          if (provider.apiKeys) {
            for (const key of provider.apiKeys) {
              keys.value.push({
                ...key,
                providerName: provider.displayName || provider.name
              })
            }
          }
        }
      } catch (error) {
        console.error('獲取金鑰資料失敗:', error)
      } finally {
        loading.value = false
      }
    }
    
    const addKey = async () => {
      submitting.value = true
      try {
        const response = await axios.post(`/api/apikeymanagement/providers/${newKey.value.provider}/keys`, {
          keyName: newKey.value.name,
          apiKey: newKey.value.apiKey,
          priority: newKey.value.priority
        })
        
        if (response.status === 200) {
          closeModal()
          await refreshData()
          alert('金鑰新增成功！')
        }
      } catch (error) {
        console.error('新增金鑰失敗:', error)
        alert('新增金鑰失敗，請稍後再試')
      } finally {
        submitting.value = false
      }
    }
    
    const editKey = (key) => {
      // TODO: 實作編輯功能
      alert('編輯功能開發中...')
    }
    
    const toggleKeyStatus = async (key) => {
      try {
        const newStatus = key.status === 'Active' ? 'Disabled' : 'Active'
        await axios.put(`/api/apikeymanagement/keys/${key.id}/status`, {
          status: newStatus
        })
        await refreshData()
        alert(`金鑰已${newStatus === 'Active' ? '啟用' : '停用'}`)
      } catch (error) {
        console.error('更新金鑰狀態失敗:', error)
        alert('操作失敗，請稍後再試')
      }
    }
    
    const deleteKey = async (key) => {
      if (!confirm(`確定要刪除金鑰 "${key.name}" 嗎？此操作無法復原。`)) {
        return
      }
      
      try {
        await axios.delete(`/api/apikeymanagement/keys/${key.id}`)
        await refreshData()
        alert('金鑰已刪除')
      } catch (error) {
        console.error('刪除金鑰失敗:', error)
        alert('刪除失敗，請稍後再試')
      }
    }
    
    const closeModal = () => {
      showAddKeyModal.value = false
      newKey.value = {
        name: '',
        apiKey: '',
        provider: 'Finnhub',
        priority: 1
      }
    }
    
    const getStatusText = (status) => {
      const statusMap = {
        'Active': '啟用',
        'Disabled': '停用',
        'Suspended': '暫停',
        'Expired': '過期'
      }
      return statusMap[status] || status
    }
    
    const formatDate = (dateString) => {
      if (!dateString) return '從未使用'
      return new Date(dateString).toLocaleString('zh-TW')
    }
    
    onMounted(() => {
      refreshData()
    })
    
    return {
      loading,
      submitting,
      showAddKeyModal,
      keys,
      newKey,
      totalKeys,
      availableKeys,
      todayUsage,
      successRate,
      handleLogout,
      refreshData,
      addKey,
      editKey,
      toggleKeyStatus,
      deleteKey,
      closeModal,
      getStatusText,
      formatDate
    }
  }
}
</script>

<style scoped>
/* 這裡使用與 Dashboard.vue 相同的樣式 */
.key-management {
  min-height: 100vh;
  background-color: #f8f9fa;
}

.navbar {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 1rem 2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.nav-brand h1 {
  margin: 0;
  font-size: 1.5rem;
  font-weight: 600;
}

.nav-menu {
  display: flex;
  gap: 1rem;
  align-items: center;
}

.nav-link {
  color: white;
  text-decoration: none;
  padding: 0.5rem 1rem;
  border-radius: 6px;
  transition: background-color 0.3s ease;
  font-weight: 500;
}

.nav-link:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.nav-link.router-link-active {
  background-color: rgba(255, 255, 255, 0.2);
}

.logout-btn {
  background: none;
  border: none;
  cursor: pointer;
  font-size: inherit;
  font-family: inherit;
}

.content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
}

.page-header {
  background: white;
  padding: 2rem;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  margin-bottom: 2rem;
}

.page-header h2 {
  margin: 0 0 1rem 0;
  color: #333;
  font-size: 2rem;
}

.page-header p {
  margin: 0;
  color: #666;
  font-size: 1.1rem;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.stat-card {
  background: white;
  padding: 1.5rem;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  gap: 1rem;
}

.stat-icon {
  font-size: 2.5rem;
}

.stat-content h3 {
  margin: 0 0 0.5rem 0;
  color: #666;
  font-size: 0.9rem;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.stat-number {
  margin: 0;
  color: #333;
  font-size: 2rem;
  font-weight: 700;
}

.actions {
  display: flex;
  gap: 1rem;
  margin-bottom: 2rem;
}

.btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  text-decoration: none;
}

.btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.btn-primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.btn-secondary {
  background: #6c757d;
  color: white;
}

.btn-success {
  background: #28a745;
  color: white;
}

.btn-warning {
  background: #ffc107;
  color: #212529;
}

.btn-danger {
  background: #dc3545;
  color: white;
}

.btn-sm {
  padding: 0.5rem 1rem;
  font-size: 0.875rem;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.key-list {
  background: white;
  padding: 2rem;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.key-list h3 {
  margin: 0 0 1.5rem 0;
  color: #333;
  font-size: 1.5rem;
}

.loading, .no-keys {
  text-align: center;
  padding: 2rem;
  color: #666;
}

.keys-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 1.5rem;
}

.key-card {
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 1.5rem;
  background: #f8f9fa;
}

.key-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.key-header h4 {
  margin: 0;
  color: #333;
}

.key-status {
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
  text-transform: uppercase;
}

.key-status.active {
  background: #d4edda;
  color: #155724;
}

.key-status.disabled {
  background: #f8d7da;
  color: #721c24;
}

.key-status.suspended {
  background: #fff3cd;
  color: #856404;
}

.key-info {
  margin-bottom: 1rem;
}

.info-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 0.5rem;
}

.label {
  font-weight: 500;
  color: #666;
}

.value {
  color: #333;
}

.key-actions {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal {
  background: white;
  border-radius: 12px;
  width: 90%;
  max-width: 500px;
  max-height: 90vh;
  overflow-y: auto;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem;
  border-bottom: 1px solid #e9ecef;
}

.modal-header h3 {
  margin: 0;
  color: #333;
}

.close-btn {
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: #666;
}

.modal-body {
  padding: 1.5rem;
}

.form-group {
  margin-bottom: 1rem;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: #333;
}

.form-group input,
.form-group select {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 1rem;
  box-sizing: border-box;
}

.form-group input:focus,
.form-group select:focus {
  outline: none;
  border-color: #667eea;
}

.modal-footer {
  display: flex;
  gap: 1rem;
  justify-content: flex-end;
  padding-top: 1rem;
  border-top: 1px solid #e9ecef;
}

@media (max-width: 768px) {
  .nav-menu {
    flex-direction: column;
    gap: 0.5rem;
  }
  
  .content {
    padding: 1rem;
  }
  
  .stats-grid {
    grid-template-columns: 1fr;
  }
  
  .keys-grid {
    grid-template-columns: 1fr;
  }
  
  .actions {
    flex-direction: column;
  }
}
</style>
