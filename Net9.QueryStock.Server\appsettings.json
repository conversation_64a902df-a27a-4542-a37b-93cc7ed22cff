{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning", "Net9.QueryStock.Server": "Information"}}, "AllowedHosts": "*", "ConnectionStrings": {"Net9QueryStockServerContext": "Server=(localdb)\\mssqllocaldb;Database=Net9QueryStockServerContext-25047311-c083-42fc-b119-028a1893a4b7;Trusted_Connection=True;MultipleActiveResultSets=true"}, "Finnhub": {"ApiKey": "YOUR_FINNHUB_API_KEY_HERE", "BaseUrl": "https://finnhub.io/api/v1", "TimeoutSeconds": 30, "RateLimitPerMinute": 60, "IsEnabled": true}, "Cache": {"DefaultExpirationMinutes": 5, "QuoteExpirationSeconds": 30, "InfoExpirationHours": 1, "HistoryExpirationHours": 4, "SearchExpirationMinutes": 10}}