using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Net9.QueryStock.Server.Models
{
    /// <summary>
    /// 股票價格實體
    /// </summary>
    public class StockPrice
    {
        public Guid Id { get; set; }
        
        /// <summary>
        /// 關聯的股票ID
        /// </summary>
        [Required]
        public Guid StockId { get; set; }
        
        /// <summary>
        /// 開盤價
        /// </summary>
        [Column(TypeName = "decimal(18,4)")]
        public decimal OpenPrice { get; set; }
        
        /// <summary>
        /// 最高價
        /// </summary>
        [Column(TypeName = "decimal(18,4)")]
        public decimal HighPrice { get; set; }
        
        /// <summary>
        /// 最低價
        /// </summary>
        [Column(TypeName = "decimal(18,4)")]
        public decimal LowPrice { get; set; }
        
        /// <summary>
        /// 收盤價
        /// </summary>
        [Column(TypeName = "decimal(18,4)")]
        public decimal ClosePrice { get; set; }
        
        /// <summary>
        /// 調整後收盤價
        /// </summary>
        [Column(TypeName = "decimal(18,4)")]
        public decimal? AdjustedClosePrice { get; set; }
        
        /// <summary>
        /// 成交量
        /// </summary>
        public long Volume { get; set; }
        
        /// <summary>
        /// 價格日期
        /// </summary>
        public DateOnly PriceDate { get; set; }
        
        /// <summary>
        /// 資料來源 (例如: Finnhub, AlphaVantage)
        /// </summary>
        [StringLength(50)]
        public string DataSource { get; set; } = string.Empty;
        
        /// <summary>
        /// 建立時間
        /// </summary>
        public DateTimeOffset CreatedAt { get; set; } = DateTimeOffset.UtcNow;
        
        /// <summary>
        /// 關聯的股票
        /// </summary>
        public virtual Stock Stock { get; set; } = null!;
    }
}
