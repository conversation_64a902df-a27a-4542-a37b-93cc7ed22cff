using Net9.QueryStock.Server.DTOs;
using Net9.QueryStock.Server.Models;

namespace Net9.QueryStock.Server.Interfaces
{
    /// <summary>
    /// 股票服務介面
    /// </summary>
    public interface IStockService
    {
        /// <summary>
        /// 獲取即時股票報價
        /// </summary>
        /// <param name="symbol">股票代號</param>
        /// <param name="cancellationToken">取消權杖</param>
        /// <returns>股票報價資訊</returns>
        Task<StockQuoteDto?> GetRealTimeQuoteAsync(string symbol, CancellationToken cancellationToken = default);
        
        /// <summary>
        /// 獲取股票詳細資訊
        /// </summary>
        /// <param name="symbol">股票代號</param>
        /// <param name="cancellationToken">取消權杖</param>
        /// <returns>股票詳細資訊</returns>
        Task<StockInfoDto?> GetStockInfoAsync(string symbol, CancellationToken cancellationToken = default);
        
        /// <summary>
        /// 獲取歷史價格資料
        /// </summary>
        /// <param name="symbol">股票代號</param>
        /// <param name="fromDate">開始日期</param>
        /// <param name="toDate">結束日期</param>
        /// <param name="cancellationToken">取消權杖</param>
        /// <returns>歷史價格資料列表</returns>
        Task<IEnumerable<StockPriceDto>> GetHistoricalPricesAsync(
            string symbol, 
            DateOnly fromDate, 
            DateOnly toDate, 
            CancellationToken cancellationToken = default);
        
        /// <summary>
        /// 搜尋股票
        /// </summary>
        /// <param name="query">搜尋關鍵字</param>
        /// <param name="cancellationToken">取消權杖</param>
        /// <returns>符合條件的股票列表</returns>
        Task<IEnumerable<StockInfoDto>> SearchStocksAsync(string query, CancellationToken cancellationToken = default);
        
        /// <summary>
        /// 批量獲取即時報價
        /// </summary>
        /// <param name="symbols">股票代號列表</param>
        /// <param name="cancellationToken">取消權杖</param>
        /// <returns>股票報價字典</returns>
        Task<Dictionary<string, StockQuoteDto>> GetBatchQuotesAsync(
            IEnumerable<string> symbols, 
            CancellationToken cancellationToken = default);
        
        /// <summary>
        /// 同步股票資料到資料庫
        /// </summary>
        /// <param name="symbol">股票代號</param>
        /// <param name="cancellationToken">取消權杖</param>
        /// <returns>同步結果</returns>
        Task<bool> SyncStockDataAsync(string symbol, CancellationToken cancellationToken = default);
        
        /// <summary>
        /// 獲取資料庫中的股票列表
        /// </summary>
        /// <param name="cancellationToken">取消權杖</param>
        /// <returns>股票列表</returns>
        Task<IEnumerable<Stock>> GetStoredStocksAsync(CancellationToken cancellationToken = default);
        
        /// <summary>
        /// 根據ID獲取股票
        /// </summary>
        /// <param name="id">股票ID</param>
        /// <param name="cancellationToken">取消權杖</param>
        /// <returns>股票資訊</returns>
        Task<Stock?> GetStockByIdAsync(Guid id, CancellationToken cancellationToken = default);
        
        /// <summary>
        /// 根據代號獲取股票
        /// </summary>
        /// <param name="symbol">股票代號</param>
        /// <param name="cancellationToken">取消權杖</param>
        /// <returns>股票資訊</returns>
        Task<Stock?> GetStockBySymbolAsync(string symbol, CancellationToken cancellationToken = default);
    }
}
