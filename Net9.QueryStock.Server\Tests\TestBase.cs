using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Net9.QueryStock.Server.Data;
using Net9.QueryStock.Server.Interfaces;
using Net9.QueryStock.Server.Services;
using Net9.QueryStock.Server.Models;
using Xunit;

namespace Net9.QueryStock.Server.Tests
{
    /// <summary>
    /// 測試基底類別，提供共用的測試設定和工具
    /// </summary>
    public abstract class TestBase : IDisposable
    {
        protected readonly IServiceProvider ServiceProvider;
        protected readonly Net9QueryStockServerContext Context;
        protected readonly IConfiguration Configuration;

        protected TestBase()
        {
            var services = new ServiceCollection();
            
            // 設定測試用的配置
            var configurationBuilder = new ConfigurationBuilder()
                .AddInMemoryCollection(new Dictionary<string, string?>
                {
                    ["ConnectionStrings:DefaultConnection"] = GetTestConnectionString(),
                    ["ApiKeyEncryption:Key"] = "TestEncryptionKey123456789012345",
                    ["Jwt:Key"] = "TestJwtSecretKey123456789012345678901234567890",
                    ["Jwt:Issuer"] = "Net9.QueryStock.Test",
                    ["Jwt:Audience"] = "Net9.QueryStock.Test.Client",
                    ["Finnhub:BaseUrl"] = "https://finnhub.io/api/v1",
                    ["Finnhub:TimeoutSeconds"] = "30",
                    ["Finnhub:IsEnabled"] = "true"
                });
            
            Configuration = configurationBuilder.Build();
            services.AddSingleton(Configuration);

            // 設定日誌
            services.AddLogging(builder => builder.AddConsole().SetMinimumLevel(LogLevel.Information));

            // 設定資料庫 - 使用實際的 SQL Server 資料庫進行測試
            services.AddDbContext<Net9QueryStockServerContext>(options =>
                options.UseSqlServer(GetTestConnectionString()));

            // 註冊服務
            services.AddScoped<IApiKeyPoolService, ApiKeyPoolService>();
            services.AddScoped<IApiKeyEncryptionService, ApiKeyEncryptionService>();
            services.AddScoped<IAuthService, AuthService>();
            services.AddScoped<IStockService, StockService>();
            services.AddScoped<DatabaseSeederService>();
            services.AddHttpClient<FinnhubDataProvider>();
            services.AddScoped<FinnhubDataProvider>();

            ServiceProvider = services.BuildServiceProvider();
            Context = ServiceProvider.GetRequiredService<Net9QueryStockServerContext>();
            
            // 確保資料庫存在並應用遷移
            Context.Database.EnsureCreated();
        }

        /// <summary>
        /// 獲取測試用的資料庫連接字串
        /// 使用獨立的測試資料庫
        /// </summary>
        protected virtual string GetTestConnectionString()
        {
            return "Server=(localdb)\\mssqllocaldb;Database=Net9QueryStockTest;Trusted_Connection=true;MultipleActiveResultSets=true";
        }

        /// <summary>
        /// 清理測試資料
        /// </summary>
        protected async Task CleanupTestDataAsync()
        {
            // 清理測試資料，但保留基本的種子資料
            Context.ApiKeyUsages.RemoveRange(Context.ApiKeyUsages);
            Context.ApiKeys.RemoveRange(Context.ApiKeys);
            Context.Users.RemoveRange(Context.Users);
            await Context.SaveChangesAsync();
        }

        /// <summary>
        /// 建立測試用的 API 金鑰提供者
        /// </summary>
        protected async Task SeedTestApiKeyProviderAsync()
        {
            var seeder = ServiceProvider.GetRequiredService<DatabaseSeederService>();
            await seeder.SeedAsync();
        }

        /// <summary>
        /// 建立測試用的 API 金鑰
        /// </summary>
        protected async Task<Models.ApiKey> CreateTestApiKeyAsync(string providerName = "Finnhub", string keyName = "TestKey", string apiKey = "test_api_key_123")
        {
            var encryptionService = ServiceProvider.GetRequiredService<IApiKeyEncryptionService>();
            var provider = await Context.ApiKeyProviders.FirstOrDefaultAsync(p => p.Name == providerName);
            
            if (provider == null)
            {
                throw new InvalidOperationException($"找不到提供者: {providerName}");
            }

            var encryptedKey = encryptionService.Encrypt(apiKey);
            var testKey = new Models.ApiKey
            {
                Id = Guid.NewGuid(),
                Name = keyName,
                EncryptedKey = encryptedKey,
                ProviderId = provider.Id,
                Priority = 1,
                RateLimitPerMinute = 60,
                RateLimitPerDay = 1000,
                Status = Models.ApiKeyStatus.Active,
                CreatedAt = DateTimeOffset.UtcNow,
                UpdatedAt = DateTimeOffset.UtcNow
            };

            Context.ApiKeys.Add(testKey);
            await Context.SaveChangesAsync();
            return testKey;
        }

        /// <summary>
        /// 建立測試用戶
        /// </summary>
        protected async Task<Models.User> CreateTestUserAsync(string username = "testuser", string password = "testpass123", string email = "<EMAIL>")
        {
            var authService = ServiceProvider.GetRequiredService<IAuthService>();
            var (hash, salt) = authService.HashPassword(password);

            var user = new Models.User
            {
                Id = Guid.NewGuid(),
                Username = username,
                Email = email,
                PasswordHash = hash,
                Salt = salt,
                Role = "User",
                IsActive = true,
                CreatedAt = DateTimeOffset.UtcNow,
                UpdatedAt = DateTimeOffset.UtcNow
            };

            Context.Users.Add(user);
            await Context.SaveChangesAsync();
            return user;
        }

        public virtual void Dispose()
        {
            Context?.Dispose();
            ServiceProvider?.GetService<IServiceScope>()?.Dispose();
        }
    }
}
