using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Net9.QueryStock.Server.Models
{
    /// <summary>
    /// API 金鑰使用記錄實體
    /// </summary>
    public class ApiKeyUsage
    {
        public Guid Id { get; set; }
        
        /// <summary>
        /// 關聯的 API 金鑰 ID
        /// </summary>
        [Required]
        public Guid ApiKeyId { get; set; }
        
        /// <summary>
        /// 請求的端點/方法
        /// </summary>
        [Required]
        [StringLength(200)]
        public string Endpoint { get; set; } = string.Empty;
        
        /// <summary>
        /// 請求參數 (JSON 格式)
        /// </summary>
        [StringLength(2000)]
        public string? RequestParameters { get; set; }
        
        /// <summary>
        /// HTTP 狀態碼
        /// </summary>
        public int? HttpStatusCode { get; set; }
        
        /// <summary>
        /// 是否成功
        /// </summary>
        public bool IsSuccess { get; set; }
        
        /// <summary>
        /// 錯誤訊息 (如果有)
        /// </summary>
        [StringLength(1000)]
        public string? ErrorMessage { get; set; }
        
        /// <summary>
        /// 回應時間 (毫秒)
        /// </summary>
        public int ResponseTimeMs { get; set; }
        
        /// <summary>
        /// 請求時間
        /// </summary>
        public DateTimeOffset RequestTime { get; set; } = DateTimeOffset.UtcNow;
        
        /// <summary>
        /// 用戶 IP 地址
        /// </summary>
        [StringLength(45)]
        public string? UserIpAddress { get; set; }
        
        /// <summary>
        /// 用戶代理
        /// </summary>
        [StringLength(500)]
        public string? UserAgent { get; set; }
        
        /// <summary>
        /// 額外的元數據 (JSON 格式)
        /// </summary>
        [StringLength(2000)]
        public string? Metadata { get; set; }
        
        /// <summary>
        /// 關聯的 API 金鑰
        /// </summary>
        public virtual ApiKey ApiKey { get; set; } = null!;
    }
}
