<script setup>
import StockSearch from './components/StockSearch.vue'
</script>

<template>
  <header>
    <div class="header-content">
      <h1>股票查詢平台</h1>
      <p>即時股票資訊查詢系統</p>
    </div>
  </header>

  <main>
    <StockSearch />
  </main>
</template>

<style scoped>
header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 2rem 0;
  text-align: center;
  margin-bottom: 2rem;
}

.header-content h1 {
  margin: 0;
  font-size: 2.5rem;
  font-weight: 300;
}

.header-content p {
  margin: 0.5rem 0 0 0;
  font-size: 1.1rem;
  opacity: 0.9;
}

main {
  min-height: calc(100vh - 200px);
}

@media (max-width: 768px) {
  .header-content h1 {
    font-size: 2rem;
  }

  .header-content p {
    font-size: 1rem;
  }
}
</style>
