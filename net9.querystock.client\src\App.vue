<script setup>
import { onMounted } from 'vue'
import { useAuthStore } from './stores/auth'
import Layout from './components/Layout.vue'

const authStore = useAuthStore()

onMounted(() => {
  // 初始化認證狀態
  authStore.initAuth()
})
</script>

<template>
  <div id="app">
    <Layout>
      <router-view />
    </Layout>
  </div>
</template>

<style>
#app {
  font-family: Avenir, Helvetica, Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

* {
  box-sizing: border-box;
}

body {
  margin: 0;
  padding: 0;
}
</style>
