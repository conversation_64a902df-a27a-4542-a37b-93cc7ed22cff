<template>
  <div class="stock-search-form">
    <!-- 搜尋表單 -->
    <div class="search-container">
      <div class="search-box">
        <input
          v-model="searchQuery"
          @keyup.enter="searchStocks"
          type="text"
          placeholder="輸入股票代碼或公司名稱 (例如: AAPL, Apple)"
          class="search-input"
          :disabled="loading"
        />
        <button @click="searchStocks" class="search-btn" :disabled="loading || !searchQuery.trim()">
          {{ loading ? '搜尋中...' : '搜尋' }}
        </button>
      </div>
    </div>

    <!-- 錯誤訊息 -->
    <div v-if="errorMessage" class="error-message">
      {{ errorMessage }}
    </div>

    <!-- 搜尋結果 -->
    <div v-if="searchResults.length > 0" class="search-results">
      <h3>搜尋結果</h3>
      <div class="results-grid">
        <div
          v-for="stock in searchResults"
          :key="stock.symbol"
          @click="selectStock(stock)"
          class="result-item"
        >
          <div class="stock-symbol">{{ stock.symbol }}</div>
          <div class="stock-name">{{ stock.description }}</div>
          <div class="stock-type">{{ stock.type }}</div>
        </div>
      </div>
    </div>

    <!-- 選中的股票詳情 -->
    <div v-if="selectedStock" class="stock-details">
      <div class="details-header">
        <h3>{{ selectedStock.symbol }} - {{ selectedStock.description }}</h3>
        <button @click="refreshQuote" class="refresh-btn" :disabled="loading">
          {{ loading ? '更新中...' : '刷新' }}
        </button>
      </div>

      <!-- 即時報價 -->
      <div v-if="currentQuote" class="quote-section">
        <div class="quote-grid">
          <div class="quote-item main-price">
            <div class="label">當前價格</div>
            <div class="value price" :class="getPriceChangeClass(currentQuote.changePercent)">
              ${{ currentQuote.currentPrice?.toFixed(2) }}
            </div>
          </div>
          <div class="quote-item">
            <div class="label">漲跌</div>
            <div class="value" :class="getPriceChangeClass(currentQuote.changePercent)">
              {{ currentQuote.change > 0 ? '+' : '' }}{{ currentQuote.change?.toFixed(2) }}
            </div>
          </div>
          <div class="quote-item">
            <div class="label">漲跌幅</div>
            <div class="value" :class="getPriceChangeClass(currentQuote.changePercent)">
              {{ currentQuote.changePercent > 0 ? '+' : '' }}{{ currentQuote.changePercent?.toFixed(2) }}%
            </div>
          </div>
          <div class="quote-item">
            <div class="label">開盤價</div>
            <div class="value">${{ currentQuote.openPrice?.toFixed(2) }}</div>
          </div>
          <div class="quote-item">
            <div class="label">最高價</div>
            <div class="value">${{ currentQuote.highPrice?.toFixed(2) }}</div>
          </div>
          <div class="quote-item">
            <div class="label">最低價</div>
            <div class="value">${{ currentQuote.lowPrice?.toFixed(2) }}</div>
          </div>
          <div class="quote-item">
            <div class="label">前收盤</div>
            <div class="value">${{ currentQuote.previousClose?.toFixed(2) }}</div>
          </div>
          <div class="quote-item">
            <div class="label">更新時間</div>
            <div class="value">{{ formatTimestamp(currentQuote.timestamp) }}</div>
          </div>
        </div>
      </div>

      <!-- 公司資訊 -->
      <div v-if="stockInfo" class="company-info">
        <h4>公司資訊</h4>
        <div class="info-grid">
          <div class="info-item">
            <div class="label">公司名稱</div>
            <div class="value">{{ stockInfo.name }}</div>
          </div>
          <div class="info-item">
            <div class="label">國家</div>
            <div class="value">{{ stockInfo.country }}</div>
          </div>
          <div class="info-item">
            <div class="label">貨幣</div>
            <div class="value">{{ stockInfo.currency }}</div>
          </div>
          <div class="info-item">
            <div class="label">交易所</div>
            <div class="value">{{ stockInfo.exchange }}</div>
          </div>
          <div class="info-item">
            <div class="label">行業</div>
            <div class="value">{{ stockInfo.finnhubIndustry }}</div>
          </div>
          <div class="info-item">
            <div class="label">市值</div>
            <div class="value">{{ formatMarketCap(stockInfo.marketCapitalization) }}</div>
          </div>
        </div>
        <div v-if="stockInfo.weburl" class="company-website">
          <a :href="stockInfo.weburl" target="_blank" class="website-link">
            訪問公司網站 ↗
          </a>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref } from 'vue'
import axios from 'axios'

export default {
  name: 'StockSearchForm',
  setup() {
    const searchQuery = ref('')
    const searchResults = ref([])
    const selectedStock = ref(null)
    const currentQuote = ref(null)
    const stockInfo = ref(null)
    const loading = ref(false)
    const errorMessage = ref('')

    const searchStocks = async () => {
      if (!searchQuery.value.trim()) return

      loading.value = true
      errorMessage.value = ''
      searchResults.value = []

      try {
        const response = await axios.get(`/api/stock/search?query=${encodeURIComponent(searchQuery.value)}`)
        searchResults.value = response.data || []
        
        if (searchResults.value.length === 0) {
          errorMessage.value = '未找到相關股票，請嘗試其他關鍵字'
        }
      } catch (error) {
        console.error('搜尋股票失敗:', error)
        if (error.response?.status === 404) {
          errorMessage.value = 'API 服務暫時無法使用，請稍後再試'
        } else {
          errorMessage.value = error.response?.data?.message || '搜尋失敗，請稍後再試'
        }
      } finally {
        loading.value = false
      }
    }

    const selectStock = async (stock) => {
      selectedStock.value = stock
      currentQuote.value = null
      stockInfo.value = null
      errorMessage.value = ''
      
      await Promise.all([
        getQuote(stock.symbol),
        getStockInfo(stock.symbol)
      ])
    }

    const getQuote = async (symbol) => {
      loading.value = true
      try {
        const response = await axios.get(`/api/stock/quote/${symbol}`)
        currentQuote.value = response.data
      } catch (error) {
        console.error('獲取報價失敗:', error)
        errorMessage.value = '獲取股票報價失敗'
      } finally {
        loading.value = false
      }
    }

    const getStockInfo = async (symbol) => {
      try {
        const response = await axios.get(`/api/stock/info/${symbol}`)
        stockInfo.value = response.data
      } catch (error) {
        console.error('獲取股票資訊失敗:', error)
      }
    }

    const refreshQuote = () => {
      if (selectedStock.value) {
        getQuote(selectedStock.value.symbol)
      }
    }

    const getPriceChangeClass = (changePercent) => {
      if (changePercent > 0) return 'positive'
      if (changePercent < 0) return 'negative'
      return 'neutral'
    }

    const formatTimestamp = (timestamp) => {
      if (!timestamp) return '未知'
      return new Date(timestamp * 1000).toLocaleString('zh-TW')
    }

    const formatMarketCap = (marketCap) => {
      if (!marketCap) return '未知'
      if (marketCap >= 1000000) {
        return `${(marketCap / 1000000).toFixed(1)}T`
      } else if (marketCap >= 1000) {
        return `${(marketCap / 1000).toFixed(1)}B`
      }
      return `${marketCap.toFixed(1)}M`
    }

    return {
      searchQuery,
      searchResults,
      selectedStock,
      currentQuote,
      stockInfo,
      loading,
      errorMessage,
      searchStocks,
      selectStock,
      refreshQuote,
      getPriceChangeClass,
      formatTimestamp,
      formatMarketCap
    }
  }
}
</script>

<style scoped>
.stock-search-form {
  max-width: 800px;
  margin: 0 auto;
}

/* 搜尋區域 */
.search-container {
  margin-bottom: 2rem;
}

.search-box {
  display: flex;
  gap: 1rem;
  max-width: 600px;
  margin: 0 auto;
}

.search-input {
  flex: 1;
  padding: 1rem;
  border: 2px solid #e1e5e9;
  border-radius: 8px;
  font-size: 1rem;
  transition: border-color 0.3s ease;
}

.search-input:focus {
  outline: none;
  border-color: #667eea;
}

.search-btn {
  padding: 1rem 2rem;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  white-space: nowrap;
}

.search-btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
}

.search-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

/* 錯誤訊息 */
.error-message {
  background-color: #fee;
  color: #c33;
  padding: 1rem;
  border-radius: 8px;
  margin-bottom: 2rem;
  border: 1px solid #fcc;
  text-align: center;
}

/* 搜尋結果 */
.search-results {
  margin-bottom: 2rem;
}

.search-results h3 {
  margin: 0 0 1rem 0;
  color: #333;
}

.results-grid {
  display: grid;
  gap: 1rem;
}

.result-item {
  padding: 1rem;
  border: 1px solid #e1e5e9;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  background: white;
}

.result-item:hover {
  border-color: #667eea;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.stock-symbol {
  font-weight: 600;
  color: #333;
  font-size: 1.1rem;
}

.stock-name {
  color: #666;
  margin: 0.25rem 0;
}

.stock-type {
  color: #999;
  font-size: 0.9rem;
}

/* 股票詳情 */
.stock-details {
  background: white;
  border-radius: 12px;
  padding: 2rem;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.details-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  flex-wrap: wrap;
  gap: 1rem;
}

.details-header h3 {
  margin: 0;
  color: #333;
  font-size: 1.5rem;
}

.refresh-btn {
  padding: 0.5rem 1rem;
  background: #6c757d;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.refresh-btn:hover:not(:disabled) {
  background: #5a6268;
}

/* 報價區域 */
.quote-section {
  margin-bottom: 2rem;
}

.quote-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
}

.quote-item {
  padding: 1rem;
  background: #f8f9fa;
  border-radius: 8px;
  text-align: center;
}

.quote-item.main-price {
  grid-column: 1 / -1;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.quote-item .label {
  font-size: 0.9rem;
  color: #666;
  margin-bottom: 0.5rem;
}

.main-price .label {
  color: rgba(255, 255, 255, 0.8);
}

.quote-item .value {
  font-size: 1.2rem;
  font-weight: 600;
  color: #333;
}

.main-price .value {
  color: white;
  font-size: 2rem;
}

.value.positive {
  color: #28a745;
}

.value.negative {
  color: #dc3545;
}

.value.neutral {
  color: #6c757d;
}

/* 公司資訊 */
.company-info h4 {
  margin: 0 0 1rem 0;
  color: #333;
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1rem;
  margin-bottom: 1rem;
}

.info-item {
  display: flex;
  justify-content: space-between;
  padding: 0.75rem;
  background: #f8f9fa;
  border-radius: 6px;
}

.info-item .label {
  font-weight: 500;
  color: #666;
}

.info-item .value {
  color: #333;
  text-align: right;
}

.company-website {
  text-align: center;
  margin-top: 1rem;
}

.website-link {
  color: #667eea;
  text-decoration: none;
  font-weight: 500;
  transition: color 0.3s ease;
}

.website-link:hover {
  color: #764ba2;
  text-decoration: underline;
}

/* 響應式設計 */
@media (max-width: 768px) {
  .search-box {
    flex-direction: column;
  }
  
  .search-btn {
    width: 100%;
  }
  
  .quote-grid {
    grid-template-columns: 1fr;
  }
  
  .quote-item.main-price {
    grid-column: 1;
  }
  
  .info-grid {
    grid-template-columns: 1fr;
  }
  
  .info-item {
    flex-direction: column;
    gap: 0.5rem;
  }
  
  .info-item .value {
    text-align: left;
  }
  
  .details-header {
    flex-direction: column;
    align-items: flex-start;
  }
}
</style>
