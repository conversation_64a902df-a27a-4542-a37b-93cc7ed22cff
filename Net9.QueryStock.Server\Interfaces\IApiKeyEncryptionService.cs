namespace Net9.QueryStock.Server.Interfaces
{
    /// <summary>
    /// API 金鑰加密服務介面
    /// </summary>
    public interface IApiKeyEncryptionService
    {
        /// <summary>
        /// 加密金鑰
        /// </summary>
        /// <param name="plainKey">明文金鑰</param>
        /// <returns>加密後的金鑰</returns>
        string EncryptKey(string plainKey);
        
        /// <summary>
        /// 解密金鑰
        /// </summary>
        /// <param name="encryptedKey">加密的金鑰</param>
        /// <returns>明文金鑰</returns>
        string DecryptKey(string encryptedKey);
        
        /// <summary>
        /// 驗證金鑰是否有效
        /// </summary>
        /// <param name="encryptedKey">加密的金鑰</param>
        /// <returns>是否有效</returns>
        bool IsValidEncryptedKey(string encryptedKey);
    }
}
