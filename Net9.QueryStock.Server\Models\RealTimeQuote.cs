using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Net9.QueryStock.Server.Models
{
    /// <summary>
    /// 即時股價報價實體
    /// </summary>
    public class RealTimeQuote
    {
        public Guid Id { get; set; }
        
        /// <summary>
        /// 股票代號
        /// </summary>
        [Required]
        [StringLength(20)]
        public string Symbol { get; set; } = string.Empty;
        
        /// <summary>
        /// 當前價格
        /// </summary>
        [Column(TypeName = "decimal(18,4)")]
        public decimal CurrentPrice { get; set; }
        
        /// <summary>
        /// 價格變動
        /// </summary>
        [Column(TypeName = "decimal(18,4)")]
        public decimal Change { get; set; }
        
        /// <summary>
        /// 價格變動百分比
        /// </summary>
        [Column(TypeName = "decimal(8,4)")]
        public decimal ChangePercent { get; set; }
        
        /// <summary>
        /// 今日開盤價
        /// </summary>
        [Column(TypeName = "decimal(18,4)")]
        public decimal? OpenPrice { get; set; }
        
        /// <summary>
        /// 今日最高價
        /// </summary>
        [Column(TypeName = "decimal(18,4)")]
        public decimal? HighPrice { get; set; }
        
        /// <summary>
        /// 今日最低價
        /// </summary>
        [Column(TypeName = "decimal(18,4)")]
        public decimal? LowPrice { get; set; }
        
        /// <summary>
        /// 前一交易日收盤價
        /// </summary>
        [Column(TypeName = "decimal(18,4)")]
        public decimal? PreviousClose { get; set; }
        
        /// <summary>
        /// 成交量
        /// </summary>
        public long? Volume { get; set; }
        
        /// <summary>
        /// 買價
        /// </summary>
        [Column(TypeName = "decimal(18,4)")]
        public decimal? BidPrice { get; set; }
        
        /// <summary>
        /// 賣價
        /// </summary>
        [Column(TypeName = "decimal(18,4)")]
        public decimal? AskPrice { get; set; }
        
        /// <summary>
        /// 買量
        /// </summary>
        public long? BidSize { get; set; }
        
        /// <summary>
        /// 賣量
        /// </summary>
        public long? AskSize { get; set; }
        
        /// <summary>
        /// 市場狀態 (Open, Closed, PreMarket, AfterHours)
        /// </summary>
        [StringLength(20)]
        public string? MarketStatus { get; set; }
        
        /// <summary>
        /// 報價時間
        /// </summary>
        public DateTimeOffset QuoteTime { get; set; }
        
        /// <summary>
        /// 資料來源
        /// </summary>
        [StringLength(50)]
        public string DataSource { get; set; } = string.Empty;
        
        /// <summary>
        /// 建立時間
        /// </summary>
        public DateTimeOffset CreatedAt { get; set; } = DateTimeOffset.UtcNow;
    }
}
