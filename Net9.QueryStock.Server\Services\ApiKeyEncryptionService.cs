using Microsoft.Extensions.Options;
using Net9.QueryStock.Server.Configuration;
using Net9.QueryStock.Server.Interfaces;
using System.Security.Cryptography;
using System.Text;

namespace Net9.QueryStock.Server.Services
{
    /// <summary>
    /// API 金鑰加密服務實作
    /// </summary>
    public class ApiKeyEncryptionService : IApiKeyEncryptionService
    {
        private readonly string _encryptionKey;
        private readonly ILogger<ApiKeyEncryptionService> _logger;

        public ApiKeyEncryptionService(
            IConfiguration configuration,
            ILogger<ApiKeyEncryptionService> logger)
        {
            _encryptionKey = configuration["ApiKeyEncryption:Key"] ?? "DefaultEncryptionKey12345678AB"; // 32 字元
            _logger = logger;
            
            if (_encryptionKey.Length != 32)
            {
                throw new ArgumentException("加密金鑰必須是 32 字元長度");
            }
        }

        /// <summary>
        /// 加密金鑰
        /// </summary>
        public string EncryptKey(string plainKey)
        {
            try
            {
                if (string.IsNullOrEmpty(plainKey))
                    throw new ArgumentException("金鑰不能為空", nameof(plainKey));

                using var aes = Aes.Create();
                aes.Key = Encoding.UTF8.GetBytes(_encryptionKey);
                aes.GenerateIV();

                using var encryptor = aes.CreateEncryptor();
                using var msEncrypt = new MemoryStream();
                using (var csEncrypt = new CryptoStream(msEncrypt, encryptor, CryptoStreamMode.Write))
                using (var swEncrypt = new StreamWriter(csEncrypt))
                {
                    swEncrypt.Write(plainKey);
                }

                var iv = aes.IV;
                var encrypted = msEncrypt.ToArray();
                var result = new byte[iv.Length + encrypted.Length];
                
                Buffer.BlockCopy(iv, 0, result, 0, iv.Length);
                Buffer.BlockCopy(encrypted, 0, result, iv.Length, encrypted.Length);

                return Convert.ToBase64String(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "加密金鑰時發生錯誤");
                throw;
            }
        }

        /// <summary>
        /// 解密金鑰
        /// </summary>
        public string DecryptKey(string encryptedKey)
        {
            try
            {
                if (string.IsNullOrEmpty(encryptedKey))
                    throw new ArgumentException("加密金鑰不能為空", nameof(encryptedKey));

                var fullCipher = Convert.FromBase64String(encryptedKey);

                using var aes = Aes.Create();
                aes.Key = Encoding.UTF8.GetBytes(_encryptionKey);

                var iv = new byte[aes.BlockSize / 8];
                var cipher = new byte[fullCipher.Length - iv.Length];

                Buffer.BlockCopy(fullCipher, 0, iv, 0, iv.Length);
                Buffer.BlockCopy(fullCipher, iv.Length, cipher, 0, cipher.Length);

                aes.IV = iv;

                using var decryptor = aes.CreateDecryptor();
                using var msDecrypt = new MemoryStream(cipher);
                using var csDecrypt = new CryptoStream(msDecrypt, decryptor, CryptoStreamMode.Read);
                using var srDecrypt = new StreamReader(csDecrypt);

                return srDecrypt.ReadToEnd();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "解密金鑰時發生錯誤");
                throw;
            }
        }

        /// <summary>
        /// 驗證金鑰是否有效
        /// </summary>
        public bool IsValidEncryptedKey(string encryptedKey)
        {
            try
            {
                if (string.IsNullOrEmpty(encryptedKey))
                    return false;

                var decrypted = DecryptKey(encryptedKey);
                return !string.IsNullOrEmpty(decrypted);
            }
            catch
            {
                return false;
            }
        }
    }
}
