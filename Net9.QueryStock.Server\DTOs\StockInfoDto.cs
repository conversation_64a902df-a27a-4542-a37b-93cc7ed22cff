namespace Net9.QueryStock.Server.DTOs
{
    /// <summary>
    /// 股票詳細資訊資料傳輸物件
    /// </summary>
    public class StockInfoDto
    {
        /// <summary>
        /// 股票代號
        /// </summary>
        public string Symbol { get; set; } = string.Empty;
        
        /// <summary>
        /// 公司名稱
        /// </summary>
        public string Name { get; set; } = string.Empty;
        
        /// <summary>
        /// 交易所
        /// </summary>
        public string? Exchange { get; set; }
        
        /// <summary>
        /// 貨幣
        /// </summary>
        public string Currency { get; set; } = string.Empty;
        
        /// <summary>
        /// 股票類型
        /// </summary>
        public string? StockType { get; set; }
        
        /// <summary>
        /// 所屬行業
        /// </summary>
        public string? Industry { get; set; }
        
        /// <summary>
        /// 所屬國家
        /// </summary>
        public string? Country { get; set; }
        
        /// <summary>
        /// 市值
        /// </summary>
        public decimal? MarketCapitalization { get; set; }
        
        /// <summary>
        /// 本益比
        /// </summary>
        public decimal? PeRatio { get; set; }
        
        /// <summary>
        /// 股價淨值比
        /// </summary>
        public decimal? PbRatio { get; set; }
        
        /// <summary>
        /// 股息殖利率
        /// </summary>
        public decimal? DividendYield { get; set; }
        
        /// <summary>
        /// 每股盈餘
        /// </summary>
        public decimal? EarningsPerShare { get; set; }
        
        /// <summary>
        /// 52週最高價
        /// </summary>
        public decimal? Week52High { get; set; }
        
        /// <summary>
        /// 52週最低價
        /// </summary>
        public decimal? Week52Low { get; set; }
        
        /// <summary>
        /// 公司描述
        /// </summary>
        public string? Description { get; set; }
        
        /// <summary>
        /// 公司網站
        /// </summary>
        public string? Website { get; set; }
        
        /// <summary>
        /// 總部地址
        /// </summary>
        public string? Headquarters { get; set; }
        
        /// <summary>
        /// 員工數量
        /// </summary>
        public int? EmployeeCount { get; set; }
        
        /// <summary>
        /// 成立日期
        /// </summary>
        public DateOnly? FoundedDate { get; set; }
        
        /// <summary>
        /// 上市日期
        /// </summary>
        public DateOnly? IpoDate { get; set; }
    }
}
