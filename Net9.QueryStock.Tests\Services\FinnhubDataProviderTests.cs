using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Moq;
using Moq.Protected;
using Net9.QueryStock.Server.Configuration;
using Net9.QueryStock.Server.Services;
using System.Net;
using System.Text.Json;

namespace Net9.QueryStock.Tests.Services
{
    /// <summary>
    /// FinnhubDataProvider 單元測試
    /// </summary>
    public class FinnhubDataProviderTests
    {
        private readonly Mock<ILogger<FinnhubDataProvider>> _mockLogger;
        private readonly FinnhubOptions _options;
        private readonly Mock<HttpMessageHandler> _mockHttpMessageHandler;
        private readonly HttpClient _httpClient;

        public FinnhubDataProviderTests()
        {
            _mockLogger = new Mock<ILogger<FinnhubDataProvider>>();
            _options = new FinnhubOptions
            {
                ApiKey = "test-api-key",
                BaseUrl = "https://finnhub.io/api/v1",
                TimeoutSeconds = 30,
                IsEnabled = true
            };
            
            _mockHttpMessageHandler = new Mock<HttpMessageHandler>();
            _httpClient = new HttpClient(_mockHttpMessageHandler.Object);
        }

        [Fact]
        public void ProviderName_ShouldReturnFinnhub()
        {
            // Arrange
            var provider = CreateProvider();

            // Act
            var result = provider.ProviderName;

            // Assert
            Assert.Equal("Finnhub", result);
        }

        [Fact]
        public void IsAvailable_WhenEnabledAndHasApiKey_ShouldReturnTrue()
        {
            // Arrange
            var provider = CreateProvider();

            // Act
            var result = provider.IsAvailable;

            // Assert
            Assert.True(result);
        }

        [Fact]
        public void IsAvailable_WhenDisabled_ShouldReturnFalse()
        {
            // Arrange
            _options.IsEnabled = false;
            var provider = CreateProvider();

            // Act
            var result = provider.IsAvailable;

            // Assert
            Assert.False(result);
        }

        [Fact]
        public void IsAvailable_WhenNoApiKey_ShouldReturnFalse()
        {
            // Arrange
            _options.ApiKey = string.Empty;
            var provider = CreateProvider();

            // Act
            var result = provider.IsAvailable;

            // Assert
            Assert.False(result);
        }

        [Fact]
        public async Task GetRealTimeQuoteAsync_WhenSuccessful_ShouldReturnQuote()
        {
            // Arrange
            var symbol = "AAPL";
            var responseJson = JsonSerializer.Serialize(new
            {
                c = 150.25m,
                d = 2.50m,
                dp = 1.69m,
                h = 152.00m,
                l = 149.50m,
                o = 150.00m,
                pc = 147.75m,
                t = 1640995200L
            });

            SetupHttpResponse(HttpStatusCode.OK, responseJson);
            var provider = CreateProvider();

            // Act
            var result = await provider.GetRealTimeQuoteAsync(symbol);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(symbol, result.Symbol);
            Assert.Equal(150.25m, result.CurrentPrice);
            Assert.Equal(2.50m, result.Change);
            Assert.Equal(1.69m, result.ChangePercent);
            Assert.Equal("Finnhub", result.DataSource);
        }

        [Fact]
        public async Task GetRealTimeQuoteAsync_WhenHttpError_ShouldReturnNull()
        {
            // Arrange
            var symbol = "INVALID";
            SetupHttpResponse(HttpStatusCode.NotFound, "");
            var provider = CreateProvider();

            // Act
            var result = await provider.GetRealTimeQuoteAsync(symbol);

            // Assert
            Assert.Null(result);
        }

        [Fact]
        public async Task GetStockInfoAsync_WhenSuccessful_ShouldReturnInfo()
        {
            // Arrange
            var symbol = "AAPL";
            var responseJson = JsonSerializer.Serialize(new
            {
                country = "US",
                currency = "USD",
                exchange = "NASDAQ",
                finnhubIndustry = "Technology",
                ipo = "1980-12-12",
                marketCapitalization = 2500000m,
                name = "Apple Inc",
                weburl = "https://www.apple.com"
            });

            SetupHttpResponse(HttpStatusCode.OK, responseJson);
            var provider = CreateProvider();

            // Act
            var result = await provider.GetStockInfoAsync(symbol);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(symbol, result.Symbol);
            Assert.Equal("Apple Inc", result.Name);
            Assert.Equal("NASDAQ", result.Exchange);
            Assert.Equal("USD", result.Currency);
            Assert.Equal("Technology", result.Industry);
            Assert.Equal("US", result.Country);
        }

        [Fact]
        public async Task SearchStocksAsync_WhenSuccessful_ShouldReturnResults()
        {
            // Arrange
            var query = "Apple";
            var responseJson = JsonSerializer.Serialize(new
            {
                count = 2,
                result = new[]
                {
                    new { symbol = "AAPL", description = "Apple Inc", type = "Common Stock" },
                    new { symbol = "AAPL.L", description = "Apple Inc - London", type = "Common Stock" }
                }
            });

            SetupHttpResponse(HttpStatusCode.OK, responseJson);
            var provider = CreateProvider();

            // Act
            var result = await provider.SearchStocksAsync(query);

            // Assert
            Assert.NotNull(result);
            var resultList = result.ToList();
            Assert.Equal(2, resultList.Count);
            Assert.Equal("AAPL", resultList[0].Symbol);
            Assert.Equal("Apple Inc", resultList[0].Name);
        }

        [Fact]
        public async Task GetHistoricalPricesAsync_WhenSuccessful_ShouldReturnPrices()
        {
            // Arrange
            var symbol = "AAPL";
            var fromDate = new DateOnly(2023, 1, 1);
            var toDate = new DateOnly(2023, 1, 2);
            
            var responseJson = JsonSerializer.Serialize(new
            {
                s = "ok",
                c = new[] { 150.25m, 151.50m },
                h = new[] { 152.00m, 153.00m },
                l = new[] { 149.50m, 150.75m },
                o = new[] { 150.00m, 150.25m },
                t = new[] { 1672531200L, 1672617600L },
                v = new[] { 1000000L, 1100000L }
            });

            SetupHttpResponse(HttpStatusCode.OK, responseJson);
            var provider = CreateProvider();

            // Act
            var result = await provider.GetHistoricalPricesAsync(symbol, fromDate, toDate);

            // Assert
            Assert.NotNull(result);
            var priceList = result.ToList();
            Assert.Equal(2, priceList.Count);
            Assert.Equal(symbol, priceList[0].Symbol);
            Assert.Equal(150.25m, priceList[0].ClosePrice);
            Assert.Equal("Finnhub", priceList[0].DataSource);
        }

        private FinnhubDataProvider CreateProvider()
        {
            var optionsWrapper = Options.Create(_options);
            return new FinnhubDataProvider(_httpClient, optionsWrapper, _mockLogger.Object);
        }

        private void SetupHttpResponse(HttpStatusCode statusCode, string content)
        {
            var response = new HttpResponseMessage(statusCode)
            {
                Content = new StringContent(content)
            };

            _mockHttpMessageHandler
                .Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(response);
        }
    }
}
