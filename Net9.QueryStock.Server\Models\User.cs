using System.ComponentModel.DataAnnotations;

namespace Net9.QueryStock.Server.Models
{
    /// <summary>
    /// 用戶實體
    /// </summary>
    public class User
    {
        public Guid Id { get; set; }
        
        /// <summary>
        /// 用戶名稱
        /// </summary>
        [Required]
        [StringLength(50)]
        public string Username { get; set; } = string.Empty;
        
        /// <summary>
        /// 電子郵件
        /// </summary>
        [StringLength(100)]
        public string? Email { get; set; }
        
        /// <summary>
        /// 密碼雜湊值
        /// </summary>
        [Required]
        [StringLength(500)]
        public string PasswordHash { get; set; } = string.Empty;
        
        /// <summary>
        /// 密碼鹽值
        /// </summary>
        [Required]
        [StringLength(100)]
        public string Salt { get; set; } = string.Empty;
        
        /// <summary>
        /// 用戶角色
        /// </summary>
        [StringLength(20)]
        public string Role { get; set; } = "User";
        
        /// <summary>
        /// 是否啟用
        /// </summary>
        public bool IsActive { get; set; } = true;
        
        /// <summary>
        /// 最後登入時間
        /// </summary>
        public DateTimeOffset? LastLoginAt { get; set; }
        
        /// <summary>
        /// 建立時間
        /// </summary>
        public DateTimeOffset CreatedAt { get; set; } = DateTimeOffset.UtcNow;
        
        /// <summary>
        /// 最後更新時間
        /// </summary>
        public DateTimeOffset UpdatedAt { get; set; } = DateTimeOffset.UtcNow;
    }
}
