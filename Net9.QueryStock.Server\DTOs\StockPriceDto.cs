namespace Net9.QueryStock.Server.DTOs
{
    /// <summary>
    /// 股票價格資料傳輸物件
    /// </summary>
    public class StockPriceDto
    {
        /// <summary>
        /// 股票代號
        /// </summary>
        public string Symbol { get; set; } = string.Empty;
        
        /// <summary>
        /// 開盤價
        /// </summary>
        public decimal OpenPrice { get; set; }
        
        /// <summary>
        /// 最高價
        /// </summary>
        public decimal HighPrice { get; set; }
        
        /// <summary>
        /// 最低價
        /// </summary>
        public decimal LowPrice { get; set; }
        
        /// <summary>
        /// 收盤價
        /// </summary>
        public decimal ClosePrice { get; set; }
        
        /// <summary>
        /// 調整後收盤價
        /// </summary>
        public decimal? AdjustedClosePrice { get; set; }
        
        /// <summary>
        /// 成交量
        /// </summary>
        public long Volume { get; set; }
        
        /// <summary>
        /// 價格日期
        /// </summary>
        public DateOnly PriceDate { get; set; }
        
        /// <summary>
        /// 資料來源
        /// </summary>
        public string DataSource { get; set; } = string.Empty;
    }
}
