{"Version": 1, "Hash": "Itg/Epbkw7fTOfBnq8G7ye1FEFmoXRNpzfO6hu4nI34=", "Source": "Net9.QueryStock.Server", "BasePath": "_content/Net9.QueryStock.Server", "Mode": "<PERSON><PERSON><PERSON>", "ManifestType": "Build", "ReferencedProjectsConfiguration": [{"Identity": "C:\\Users\\<USER>\\source\\repos\\Net9.QueryStock\\net9.querystock.client\\net9.querystock.client.esproj", "Version": 2, "Source": "net9.querystock.client", "GetPublishAssetsTargets": "GetCurrentProjectPublishStaticWebAssetItems", "AdditionalPublishProperties": "Configuration=Debug;Platform=AnyCPU", "AdditionalPublishPropertiesToRemove": "WebPublishProfileFile;TargetFramework", "GetBuildAssetsTargets": "GetCurrentProjectBuildStaticWebAssetItems", "AdditionalBuildProperties": "Configuration=Debug;Platform=AnyCPU", "AdditionalBuildPropertiesToRemove": "WebPublishProfileFile;TargetFramework"}], "DiscoveryPatterns": [], "Assets": [], "Endpoints": []}