{"Version": 1, "Hash": "jn+XhIfwjFGbpkpZto9w/Uit52K2uJW9TuKrnmRbAUY=", "Source": "Net9.QueryStock.Server", "BasePath": "_content/Net9.QueryStock.Server", "Mode": "<PERSON><PERSON><PERSON>", "ManifestType": "Build", "ReferencedProjectsConfiguration": [{"Identity": "C:\\Users\\<USER>\\source\\repos\\Net9.QueryStock\\net9.querystock.client\\net9.querystock.client.esproj", "Version": 2, "Source": "net9.querystock.client", "GetPublishAssetsTargets": "GetCurrentProjectPublishStaticWebAssetItems", "AdditionalPublishProperties": "", "AdditionalPublishPropertiesToRemove": "WebPublishProfileFile;TargetFramework;RuntimeIdentifier;SelfContained", "GetBuildAssetsTargets": "GetCurrentProjectBuildStaticWebAssetItems", "AdditionalBuildProperties": "", "AdditionalBuildPropertiesToRemove": "WebPublishProfileFile;TargetFramework;RuntimeIdentifier;SelfContained"}], "DiscoveryPatterns": [], "Assets": [], "Endpoints": []}