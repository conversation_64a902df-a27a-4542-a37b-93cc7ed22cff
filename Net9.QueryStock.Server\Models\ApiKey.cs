using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Net9.QueryStock.Server.Models
{
    /// <summary>
    /// API 金鑰狀態列舉
    /// </summary>
    public enum ApiKeyStatus
    {
        /// <summary>
        /// 啟用中
        /// </summary>
        Active = 1,
        
        /// <summary>
        /// 已停用
        /// </summary>
        Disabled = 2,
        
        /// <summary>
        /// 已暫停 (因為達到限制或錯誤)
        /// </summary>
        Suspended = 3,
        
        /// <summary>
        /// 已過期
        /// </summary>
        Expired = 4
    }

    /// <summary>
    /// API 金鑰實體
    /// </summary>
    public class ApiKey
    {
        public Guid Id { get; set; }
        
        /// <summary>
        /// 關聯的服務提供者 ID
        /// </summary>
        [Required]
        public Guid ProviderId { get; set; }
        
        /// <summary>
        /// 金鑰名稱/別名
        /// </summary>
        [Required]
        [StringLength(100)]
        public string Name { get; set; } = string.Empty;
        
        /// <summary>
        /// 加密後的金鑰值
        /// </summary>
        [Required]
        [StringLength(500)]
        public string EncryptedKey { get; set; } = string.Empty;
        
        /// <summary>
        /// 金鑰狀態
        /// </summary>
        public ApiKeyStatus Status { get; set; } = ApiKeyStatus.Active;
        
        /// <summary>
        /// 每分鐘請求限制 (覆蓋提供者設定)
        /// </summary>
        public int? RateLimitPerMinute { get; set; }
        
        /// <summary>
        /// 每日請求限制 (覆蓋提供者設定)
        /// </summary>
        public int? RateLimitPerDay { get; set; }
        
        /// <summary>
        /// 優先級 (數字越小優先級越高)
        /// </summary>
        public int Priority { get; set; } = 1;
        
        /// <summary>
        /// 金鑰過期時間
        /// </summary>
        public DateTimeOffset? ExpiresAt { get; set; }
        
        /// <summary>
        /// 最後使用時間
        /// </summary>
        public DateTimeOffset? LastUsedAt { get; set; }
        
        /// <summary>
        /// 總使用次數
        /// </summary>
        public long TotalUsageCount { get; set; } = 0;
        
        /// <summary>
        /// 今日使用次數
        /// </summary>
        public int TodayUsageCount { get; set; } = 0;
        
        /// <summary>
        /// 當前分鐘使用次數
        /// </summary>
        public int CurrentMinuteUsageCount { get; set; } = 0;
        
        /// <summary>
        /// 當前分鐘開始時間
        /// </summary>
        public DateTimeOffset CurrentMinuteStartTime { get; set; } = DateTimeOffset.UtcNow;
        
        /// <summary>
        /// 連續錯誤次數
        /// </summary>
        public int ConsecutiveErrorCount { get; set; } = 0;
        
        /// <summary>
        /// 最後錯誤時間
        /// </summary>
        public DateTimeOffset? LastErrorAt { get; set; }
        
        /// <summary>
        /// 最後錯誤訊息
        /// </summary>
        [StringLength(1000)]
        public string? LastErrorMessage { get; set; }
        
        /// <summary>
        /// 暫停到期時間 (如果被暫停)
        /// </summary>
        public DateTimeOffset? SuspendedUntil { get; set; }
        
        /// <summary>
        /// 建立時間
        /// </summary>
        public DateTimeOffset CreatedAt { get; set; } = DateTimeOffset.UtcNow;
        
        /// <summary>
        /// 最後更新時間
        /// </summary>
        public DateTimeOffset UpdatedAt { get; set; } = DateTimeOffset.UtcNow;
        
        /// <summary>
        /// 關聯的服務提供者
        /// </summary>
        public virtual ApiKeyProvider Provider { get; set; } = null!;
        
        /// <summary>
        /// 使用記錄
        /// </summary>
        public virtual ICollection<ApiKeyUsage> UsageRecords { get; set; } = new List<ApiKeyUsage>();
        
        /// <summary>
        /// 檢查金鑰是否可用
        /// </summary>
        public bool IsAvailable
        {
            get
            {
                if (Status != ApiKeyStatus.Active)
                    return false;
                    
                if (ExpiresAt.HasValue && ExpiresAt.Value <= DateTimeOffset.UtcNow)
                    return false;
                    
                if (SuspendedUntil.HasValue && SuspendedUntil.Value > DateTimeOffset.UtcNow)
                    return false;
                    
                return true;
            }
        }
        
        /// <summary>
        /// 檢查是否達到分鐘限制
        /// </summary>
        public bool IsMinuteRateLimitExceeded(int limit)
        {
            var now = DateTimeOffset.UtcNow;
            var minuteStart = new DateTimeOffset(now.Year, now.Month, now.Day, now.Hour, now.Minute, 0, now.Offset);
            
            // 如果是新的分鐘，重置計數
            if (CurrentMinuteStartTime < minuteStart)
            {
                return false;
            }
            
            return CurrentMinuteUsageCount >= limit;
        }
        
        /// <summary>
        /// 檢查是否達到每日限制
        /// </summary>
        public bool IsDailyRateLimitExceeded(int limit)
        {
            return TodayUsageCount >= limit;
        }
    }
}
