using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Net9.QueryStock.Server.Models
{
    /// <summary>
    /// 股票詳細資訊實體
    /// </summary>
    public class StockInfo
    {
        public Guid Id { get; set; }
        
        /// <summary>
        /// 關聯的股票ID
        /// </summary>
        [Required]
        public Guid StockId { get; set; }
        
        /// <summary>
        /// 市值
        /// </summary>
        [Column(TypeName = "decimal(18,2)")]
        public decimal? MarketCapitalization { get; set; }
        
        /// <summary>
        /// 本益比 (P/E Ratio)
        /// </summary>
        [Column(TypeName = "decimal(10,2)")]
        public decimal? PeRatio { get; set; }
        
        /// <summary>
        /// 股價淨值比 (P/B Ratio)
        /// </summary>
        [Column(TypeName = "decimal(10,2)")]
        public decimal? PbRatio { get; set; }
        
        /// <summary>
        /// 股息殖利率
        /// </summary>
        [Column(TypeName = "decimal(5,4)")]
        public decimal? DividendYield { get; set; }
        
        /// <summary>
        /// 每股盈餘 (EPS)
        /// </summary>
        [Column(TypeName = "decimal(10,4)")]
        public decimal? EarningsPerShare { get; set; }
        
        /// <summary>
        /// 52週最高價
        /// </summary>
        [Column(TypeName = "decimal(18,4)")]
        public decimal? Week52High { get; set; }
        
        /// <summary>
        /// 52週最低價
        /// </summary>
        [Column(TypeName = "decimal(18,4)")]
        public decimal? Week52Low { get; set; }
        
        /// <summary>
        /// 平均成交量 (50日)
        /// </summary>
        public long? AverageVolume50Day { get; set; }
        
        /// <summary>
        /// 平均成交量 (200日)
        /// </summary>
        public long? AverageVolume200Day { get; set; }
        
        /// <summary>
        /// 流通股數
        /// </summary>
        public long? SharesOutstanding { get; set; }
        
        /// <summary>
        /// 公司描述
        /// </summary>
        [StringLength(2000)]
        public string? Description { get; set; }
        
        /// <summary>
        /// 公司網站
        /// </summary>
        [StringLength(500)]
        public string? Website { get; set; }
        
        /// <summary>
        /// 總部地址
        /// </summary>
        [StringLength(500)]
        public string? Headquarters { get; set; }
        
        /// <summary>
        /// 員工數量
        /// </summary>
        public int? EmployeeCount { get; set; }
        
        /// <summary>
        /// 成立日期
        /// </summary>
        public DateOnly? FoundedDate { get; set; }
        
        /// <summary>
        /// 上市日期
        /// </summary>
        public DateOnly? IpoDate { get; set; }
        
        /// <summary>
        /// 最後更新時間
        /// </summary>
        public DateTimeOffset UpdatedAt { get; set; } = DateTimeOffset.UtcNow;
        
        /// <summary>
        /// 關聯的股票
        /// </summary>
        public virtual Stock Stock { get; set; } = null!;
    }
}
