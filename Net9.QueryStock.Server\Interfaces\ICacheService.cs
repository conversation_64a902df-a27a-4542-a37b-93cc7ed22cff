namespace Net9.QueryStock.Server.Interfaces
{
    /// <summary>
    /// 快取服務介面
    /// </summary>
    public interface ICacheService
    {
        /// <summary>
        /// 獲取快取資料
        /// </summary>
        /// <typeparam name="T">資料類型</typeparam>
        /// <param name="key">快取鍵</param>
        /// <param name="cancellationToken">取消權杖</param>
        /// <returns>快取資料</returns>
        Task<T?> GetAsync<T>(string key, CancellationToken cancellationToken = default) where T : class;
        
        /// <summary>
        /// 設定快取資料
        /// </summary>
        /// <typeparam name="T">資料類型</typeparam>
        /// <param name="key">快取鍵</param>
        /// <param name="value">快取值</param>
        /// <param name="expiration">過期時間</param>
        /// <param name="cancellationToken">取消權杖</param>
        /// <returns>設定結果</returns>
        Task<bool> SetAsync<T>(string key, T value, TimeSpan? expiration = null, CancellationToken cancellationToken = default) where T : class;
        
        /// <summary>
        /// 移除快取資料
        /// </summary>
        /// <param name="key">快取鍵</param>
        /// <param name="cancellationToken">取消權杖</param>
        /// <returns>移除結果</returns>
        Task<bool> RemoveAsync(string key, CancellationToken cancellationToken = default);
        
        /// <summary>
        /// 檢查快取是否存在
        /// </summary>
        /// <param name="key">快取鍵</param>
        /// <param name="cancellationToken">取消權杖</param>
        /// <returns>是否存在</returns>
        Task<bool> ExistsAsync(string key, CancellationToken cancellationToken = default);
        
        /// <summary>
        /// 獲取或設定快取資料
        /// </summary>
        /// <typeparam name="T">資料類型</typeparam>
        /// <param name="key">快取鍵</param>
        /// <param name="factory">資料工廠方法</param>
        /// <param name="expiration">過期時間</param>
        /// <param name="cancellationToken">取消權杖</param>
        /// <returns>快取資料</returns>
        Task<T?> GetOrSetAsync<T>(
            string key, 
            Func<Task<T?>> factory, 
            TimeSpan? expiration = null, 
            CancellationToken cancellationToken = default) where T : class;
        
        /// <summary>
        /// 批量移除快取資料
        /// </summary>
        /// <param name="pattern">快取鍵模式</param>
        /// <param name="cancellationToken">取消權杖</param>
        /// <returns>移除的數量</returns>
        Task<int> RemoveByPatternAsync(string pattern, CancellationToken cancellationToken = default);
    }
}
