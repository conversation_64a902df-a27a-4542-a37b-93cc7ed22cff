using System.Text.Json.Serialization;

namespace Net9.QueryStock.Server.Models.Finnhub
{
    /// <summary>
    /// Finnhub 即時報價回應模型
    /// </summary>
    public class FinnhubQuoteResponse
    {
        /// <summary>
        /// 當前價格
        /// </summary>
        [JsonPropertyName("c")]
        public decimal CurrentPrice { get; set; }
        
        /// <summary>
        /// 價格變動
        /// </summary>
        [JsonPropertyName("d")]
        public decimal Change { get; set; }
        
        /// <summary>
        /// 價格變動百分比
        /// </summary>
        [JsonPropertyName("dp")]
        public decimal ChangePercent { get; set; }
        
        /// <summary>
        /// 今日最高價
        /// </summary>
        [JsonPropertyName("h")]
        public decimal HighPrice { get; set; }
        
        /// <summary>
        /// 今日最低價
        /// </summary>
        [JsonPropertyName("l")]
        public decimal LowPrice { get; set; }
        
        /// <summary>
        /// 今日開盤價
        /// </summary>
        [JsonPropertyName("o")]
        public decimal OpenPrice { get; set; }
        
        /// <summary>
        /// 前一交易日收盤價
        /// </summary>
        [JsonPropertyName("pc")]
        public decimal PreviousClose { get; set; }
        
        /// <summary>
        /// 時間戳記
        /// </summary>
        [JsonPropertyName("t")]
        public long Timestamp { get; set; }
    }
}
