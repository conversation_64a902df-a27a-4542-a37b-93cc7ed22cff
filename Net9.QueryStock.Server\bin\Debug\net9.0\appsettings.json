{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning", "Net9.QueryStock.Server": "Information"}}, "AllowedHosts": "*", "ConnectionStrings": {"Net9QueryStockServerContext": "Data Source=.;Initial Catalog=QueryStock;Integrated Security=True;Trust Server Certificate=True"}, "Finnhub": {"BaseUrl": "https://finnhub.io/api/v1", "TimeoutSeconds": 30, "IsEnabled": true}, "Cache": {"DefaultExpirationMinutes": 5, "QuoteExpirationSeconds": 30, "InfoExpirationHours": 1, "HistoryExpirationHours": 4, "SearchExpirationMinutes": 10}, "ApiKeyEncryption": {"Key": "MySecretEncryptionKey123456789AB"}, "Jwt": {"Key": "MyJwtSecretKey123456789012345678901234567890", "Issuer": "Net9.QueryStock.Server", "Audience": "Net9.QueryStock.Client", "ExpirationHours": 24}}