{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning", "Net9.QueryStock.Server": "Information"}}, "AllowedHosts": "*", "ConnectionStrings": {"Net9QueryStockServerContext": "Data Source=.;Initial Catalog=QueryStock;Integrated Security=True;Trust Server Certificate=True"}, "Finnhub": {"ApiKey": "YOUR_FINNHUB_API_KEY_HERE", "BaseUrl": "https://finnhub.io/api/v1", "TimeoutSeconds": 30, "RateLimitPerMinute": 60, "IsEnabled": true}, "Cache": {"DefaultExpirationMinutes": 5, "QuoteExpirationSeconds": 30, "InfoExpirationHours": 1, "HistoryExpirationHours": 4, "SearchExpirationMinutes": 10}, "ApiKeyEncryption": {"Key": "MySecretEncryptionKey123456789012"}}