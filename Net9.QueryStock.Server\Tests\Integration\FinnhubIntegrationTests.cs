using Microsoft.Extensions.DependencyInjection;
using Net9.QueryStock.Server.Services;
using Xunit;

namespace Net9.QueryStock.Server.Tests.Integration
{
    /// <summary>
    /// Finnhub API 整合測試
    /// 這些測試會實際調用 Finnhub API，需要有效的 API 金鑰
    /// 如果沒有 API 金鑰，測試會被跳過或失敗
    /// </summary>
    public class FinnhubIntegrationTests : TestBase
    {
        private readonly FinnhubDataProvider _finnhubProvider;
        private const string TEST_API_KEY = "YOUR_ACTUAL_FINNHUB_API_KEY_HERE"; // 請替換為實際的 API 金鑰

        public FinnhubIntegrationTests()
        {
            _finnhubProvider = ServiceProvider.GetRequiredService<FinnhubDataProvider>();
        }

        [Fact]
        public async Task SearchStocksAsync_WithRealApiKey_ReturnsActualResults()
        {
            // Arrange
            await CleanupTestDataAsync();
            await SeedTestApiKeyProviderAsync();
            
            // 跳過測試如果沒有實際的 API 金鑰
            if (TEST_API_KEY == "YOUR_ACTUAL_FINNHUB_API_KEY_HERE")
            {
                Skip.If(true, "需要實際的 Finnhub API 金鑰才能執行此測試");
                return;
            }
            
            await CreateTestApiKeyAsync("Finnhub", "RealTestKey", TEST_API_KEY);

            // Act
            var results = await _finnhubProvider.SearchStocksAsync("Apple");

            // Assert
            Assert.NotNull(results);
            Assert.True(results.Any(), "應該返回 Apple 相關的股票結果");
            
            var appleStock = results.FirstOrDefault(r => r.Symbol.Contains("AAPL"));
            if (appleStock != null)
            {
                Assert.Contains("Apple", appleStock.Description);
                Assert.Equal("Common Stock", appleStock.Type);
            }
        }

        [Fact]
        public async Task GetQuoteAsync_WithRealApiKey_ReturnsValidQuote()
        {
            // Arrange
            await CleanupTestDataAsync();
            await SeedTestApiKeyProviderAsync();
            
            if (TEST_API_KEY == "YOUR_ACTUAL_FINNHUB_API_KEY_HERE")
            {
                Skip.If(true, "需要實際的 Finnhub API 金鑰才能執行此測試");
                return;
            }
            
            await CreateTestApiKeyAsync("Finnhub", "RealTestKey", TEST_API_KEY);

            // Act
            var quote = await _finnhubProvider.GetQuoteAsync("AAPL");

            // Assert
            Assert.NotNull(quote);
            Assert.Equal("AAPL", quote.Symbol);
            Assert.True(quote.CurrentPrice > 0, "Apple 股價應該大於 0");
            Assert.True(quote.Timestamp > 0, "時間戳應該有效");
            Assert.True(quote.OpenPrice >= 0, "開盤價應該非負");
            Assert.True(quote.HighPrice >= 0, "最高價應該非負");
            Assert.True(quote.LowPrice >= 0, "最低價應該非負");
            Assert.True(quote.PreviousClose >= 0, "前收盤價應該非負");
        }

        [Fact]
        public async Task GetStockInfoAsync_WithRealApiKey_ReturnsValidInfo()
        {
            // Arrange
            await CleanupTestDataAsync();
            await SeedTestApiKeyProviderAsync();
            
            if (TEST_API_KEY == "YOUR_ACTUAL_FINNHUB_API_KEY_HERE")
            {
                Skip.If(true, "需要實際的 Finnhub API 金鑰才能執行此測試");
                return;
            }
            
            await CreateTestApiKeyAsync("Finnhub", "RealTestKey", TEST_API_KEY);

            // Act
            var info = await _finnhubProvider.GetStockInfoAsync("AAPL");

            // Assert
            Assert.NotNull(info);
            Assert.Equal("AAPL", info.Symbol);
            Assert.Contains("Apple", info.Name);
            Assert.Equal("US", info.Country);
            Assert.Equal("USD", info.Currency);
            Assert.NotNull(info.Exchange);
            Assert.True(info.MarketCapitalization > 0, "Apple 市值應該大於 0");
        }

        [Fact]
        public async Task GetHistoricalPricesAsync_WithRealApiKey_ReturnsValidData()
        {
            // Arrange
            await CleanupTestDataAsync();
            await SeedTestApiKeyProviderAsync();
            
            if (TEST_API_KEY == "YOUR_ACTUAL_FINNHUB_API_KEY_HERE")
            {
                Skip.If(true, "需要實際的 Finnhub API 金鑰才能執行此測試");
                return;
            }
            
            await CreateTestApiKeyAsync("Finnhub", "RealTestKey", TEST_API_KEY);

            var fromDate = DateTime.UtcNow.AddDays(-30);
            var toDate = DateTime.UtcNow.AddDays(-1); // 昨天，避免當天資料不完整

            // Act
            var prices = await _finnhubProvider.GetHistoricalPricesAsync("AAPL", fromDate, toDate);

            // Assert
            Assert.NotNull(prices);
            Assert.True(prices.Any(), "應該返回歷史價格資料");
            
            foreach (var price in prices)
            {
                Assert.True(price.Date >= fromDate && price.Date <= toDate, "日期應該在指定範圍內");
                Assert.True(price.Open > 0, "開盤價應該大於 0");
                Assert.True(price.High > 0, "最高價應該大於 0");
                Assert.True(price.Low > 0, "最低價應該大於 0");
                Assert.True(price.Close > 0, "收盤價應該大於 0");
                Assert.True(price.Volume >= 0, "成交量應該非負");
                
                // 基本的價格邏輯檢查
                Assert.True(price.High >= price.Low, "最高價應該大於等於最低價");
                Assert.True(price.High >= price.Open, "最高價應該大於等於開盤價");
                Assert.True(price.High >= price.Close, "最高價應該大於等於收盤價");
                Assert.True(price.Low <= price.Open, "最低價應該小於等於開盤價");
                Assert.True(price.Low <= price.Close, "最低價應該小於等於收盤價");
            }
        }

        [Fact]
        public async Task ApiRateLimit_HandledGracefully()
        {
            // Arrange
            await CleanupTestDataAsync();
            await SeedTestApiKeyProviderAsync();
            
            if (TEST_API_KEY == "YOUR_ACTUAL_FINNHUB_API_KEY_HERE")
            {
                Skip.If(true, "需要實際的 Finnhub API 金鑰才能執行此測試");
                return;
            }
            
            await CreateTestApiKeyAsync("Finnhub", "RealTestKey", TEST_API_KEY);

            var symbols = new[] { "AAPL", "GOOGL", "MSFT", "TSLA", "AMZN", "META", "NVDA", "NFLX" };
            var tasks = new List<Task>();

            // Act - 快速發送多個請求測試速率限制
            foreach (var symbol in symbols)
            {
                tasks.Add(_finnhubProvider.GetQuoteAsync(symbol));
            }

            // 應該不會拋出未處理的異常
            var results = await Task.WhenAll(tasks);

            // Assert
            Assert.Equal(symbols.Length, results.Length);
            
            // 至少應該有一些成功的結果
            var successfulResults = results.Where(r => r != null && r.CurrentPrice > 0).ToList();
            Assert.True(successfulResults.Count > 0, "至少應該有一些成功的 API 調用");
        }

        [Fact]
        public async Task InvalidSymbol_HandledGracefully()
        {
            // Arrange
            await CleanupTestDataAsync();
            await SeedTestApiKeyProviderAsync();
            
            if (TEST_API_KEY == "YOUR_ACTUAL_FINNHUB_API_KEY_HERE")
            {
                Skip.If(true, "需要實際的 Finnhub API 金鑰才能執行此測試");
                return;
            }
            
            await CreateTestApiKeyAsync("Finnhub", "RealTestKey", TEST_API_KEY);

            // Act
            var quote = await _finnhubProvider.GetQuoteAsync("INVALID_SYMBOL_12345");
            var info = await _finnhubProvider.GetStockInfoAsync("INVALID_SYMBOL_12345");
            var search = await _finnhubProvider.SearchStocksAsync("NONEXISTENT_COMPANY_XYZ");

            // Assert
            // 對於無效的股票代碼，API 應該優雅地處理
            Assert.True(quote == null || quote.CurrentPrice == 0);
            Assert.True(info == null || string.IsNullOrEmpty(info.Name));
            Assert.True(search == null || !search.Any());
        }

        [Fact]
        public async Task NetworkTimeout_HandledGracefully()
        {
            // Arrange
            await CleanupTestDataAsync();
            await SeedTestApiKeyProviderAsync();
            
            if (TEST_API_KEY == "YOUR_ACTUAL_FINNHUB_API_KEY_HERE")
            {
                Skip.If(true, "需要實際的 Finnhub API 金鑰才能執行此測試");
                return;
            }
            
            await CreateTestApiKeyAsync("Finnhub", "RealTestKey", TEST_API_KEY);

            // Act & Assert
            // 這個測試驗證即使在網路問題的情況下，服務也不會崩潰
            try
            {
                var quote = await _finnhubProvider.GetQuoteAsync("AAPL");
                // 如果成功，驗證結果
                if (quote != null)
                {
                    Assert.True(quote.CurrentPrice >= 0);
                }
            }
            catch (HttpRequestException)
            {
                // 網路異常是可以接受的
                Assert.True(true);
            }
            catch (TaskCanceledException)
            {
                // 超時異常也是可以接受的
                Assert.True(true);
            }
        }

        [Fact]
        public async Task ApiKeyUsage_RecordedCorrectly()
        {
            // Arrange
            await CleanupTestDataAsync();
            await SeedTestApiKeyProviderAsync();
            
            if (TEST_API_KEY == "YOUR_ACTUAL_FINNHUB_API_KEY_HERE")
            {
                Skip.If(true, "需要實際的 Finnhub API 金鑰才能執行此測試");
                return;
            }
            
            var testKey = await CreateTestApiKeyAsync("Finnhub", "UsageTestKey", TEST_API_KEY);

            // Act
            await _finnhubProvider.GetQuoteAsync("AAPL");
            
            // 等待使用記錄被寫入
            await Task.Delay(500);

            // Assert
            var usageRecords = Context.ApiKeyUsages.Where(u => u.ApiKeyId == testKey.Id).ToList();
            
            if (usageRecords.Any())
            {
                var record = usageRecords.First();
                Assert.NotNull(record.Endpoint);
                Assert.True(record.ResponseTimeMs >= 0);
                Assert.True(record.UsedAt <= DateTimeOffset.UtcNow);
                Assert.True(record.HttpStatusCode >= 200 && record.HttpStatusCode < 600);
            }
        }

        [Fact]
        public async Task ConcurrentRequests_HandledCorrectly()
        {
            // Arrange
            await CleanupTestDataAsync();
            await SeedTestApiKeyProviderAsync();
            
            if (TEST_API_KEY == "YOUR_ACTUAL_FINNHUB_API_KEY_HERE")
            {
                Skip.If(true, "需要實際的 Finnhub API 金鑰才能執行此測試");
                return;
            }
            
            await CreateTestApiKeyAsync("Finnhub", "ConcurrentTestKey", TEST_API_KEY);

            var symbols = new[] { "AAPL", "GOOGL", "MSFT" };

            // Act
            var quoteTasks = symbols.Select(symbol => _finnhubProvider.GetQuoteAsync(symbol));
            var infoTasks = symbols.Select(symbol => _finnhubProvider.GetStockInfoAsync(symbol));
            var searchTasks = symbols.Select(symbol => _finnhubProvider.SearchStocksAsync(symbol));

            var allTasks = quoteTasks.Concat(infoTasks).Concat(searchTasks);
            await Task.WhenAll(allTasks);

            // Assert
            // 如果到達這裡，表示所有並發請求都已完成且沒有未處理的異常
            Assert.True(true);
        }
    }
}
