<template>
  <div class="stock-search">
    <h2>股票查詢系統</h2>
    
    <!-- 搜尋區域 -->
    <div class="search-section">
      <div class="search-input-group">
        <input 
          v-model="searchQuery" 
          @keyup.enter="searchStocks"
          type="text" 
          placeholder="輸入股票代號或公司名稱 (例如: AAPL, Apple)"
          class="search-input"
        />
        <button @click="searchStocks" :disabled="loading" class="search-button">
          {{ loading ? '搜尋中...' : '搜尋' }}
        </button>
      </div>
    </div>

    <!-- 搜尋結果 -->
    <div v-if="searchResults.length > 0" class="search-results">
      <h3>搜尋結果</h3>
      <div class="stock-grid">
        <div 
          v-for="stock in searchResults" 
          :key="stock.symbol"
          @click="selectStock(stock.symbol)"
          class="stock-card clickable"
        >
          <div class="stock-symbol">{{ stock.symbol }}</div>
          <div class="stock-name">{{ stock.name }}</div>
          <div class="stock-exchange">{{ stock.exchange || 'N/A' }}</div>
        </div>
      </div>
    </div>

    <!-- 選中的股票詳細資訊 -->
    <div v-if="selectedStock" class="stock-details">
      <h3>{{ selectedStock.symbol }} - {{ selectedStock.name }}</h3>
      
      <!-- 即時報價 -->
      <div v-if="currentQuote" class="quote-section">
        <h4>即時報價</h4>
        <div class="quote-grid">
          <div class="quote-item">
            <span class="label">當前價格:</span>
            <span class="value price" :class="{ 'positive': currentQuote.change > 0, 'negative': currentQuote.change < 0 }">
              ${{ currentQuote.currentPrice.toFixed(2) }}
            </span>
          </div>
          <div class="quote-item">
            <span class="label">漲跌:</span>
            <span class="value" :class="{ 'positive': currentQuote.change > 0, 'negative': currentQuote.change < 0 }">
              {{ currentQuote.change > 0 ? '+' : '' }}{{ currentQuote.change.toFixed(2) }} 
              ({{ currentQuote.changePercent.toFixed(2) }}%)
            </span>
          </div>
          <div class="quote-item">
            <span class="label">開盤價:</span>
            <span class="value">${{ currentQuote.openPrice?.toFixed(2) || 'N/A' }}</span>
          </div>
          <div class="quote-item">
            <span class="label">最高價:</span>
            <span class="value">${{ currentQuote.highPrice?.toFixed(2) || 'N/A' }}</span>
          </div>
          <div class="quote-item">
            <span class="label">最低價:</span>
            <span class="value">${{ currentQuote.lowPrice?.toFixed(2) || 'N/A' }}</span>
          </div>
          <div class="quote-item">
            <span class="label">成交量:</span>
            <span class="value">{{ formatVolume(currentQuote.volume) }}</span>
          </div>
        </div>
      </div>

      <!-- 公司資訊 -->
      <div v-if="stockInfo" class="info-section">
        <h4>公司資訊</h4>
        <div class="info-grid">
          <div class="info-item">
            <span class="label">交易所:</span>
            <span class="value">{{ stockInfo.exchange || 'N/A' }}</span>
          </div>
          <div class="info-item">
            <span class="label">貨幣:</span>
            <span class="value">{{ stockInfo.currency || 'N/A' }}</span>
          </div>
          <div class="info-item">
            <span class="label">行業:</span>
            <span class="value">{{ stockInfo.industry || 'N/A' }}</span>
          </div>
          <div class="info-item">
            <span class="label">國家:</span>
            <span class="value">{{ stockInfo.country || 'N/A' }}</span>
          </div>
          <div class="info-item">
            <span class="label">市值:</span>
            <span class="value">{{ formatMarketCap(stockInfo.marketCapitalization) }}</span>
          </div>
          <div class="info-item">
            <span class="label">網站:</span>
            <span class="value">
              <a v-if="stockInfo.website" :href="stockInfo.website" target="_blank">{{ stockInfo.website }}</a>
              <span v-else>N/A</span>
            </span>
          </div>
        </div>
      </div>

      <!-- 操作按鈕 -->
      <div class="action-buttons">
        <button @click="refreshQuote" :disabled="loading" class="refresh-button">
          {{ loading ? '更新中...' : '更新報價' }}
        </button>
        <button @click="syncToDatabase" :disabled="loading" class="sync-button">
          {{ loading ? '同步中...' : '同步到資料庫' }}
        </button>
      </div>
    </div>

    <!-- 錯誤訊息 -->
    <div v-if="errorMessage" class="error-message">
      {{ errorMessage }}
    </div>
  </div>
</template>

<script>
export default {
  name: 'StockSearch',
  data() {
    return {
      searchQuery: '',
      searchResults: [],
      selectedStock: null,
      currentQuote: null,
      stockInfo: null,
      loading: false,
      errorMessage: ''
    };
  },
  methods: {
    async searchStocks() {
      if (!this.searchQuery.trim()) {
        this.errorMessage = '請輸入搜尋關鍵字';
        return;
      }

      this.loading = true;
      this.errorMessage = '';
      
      try {
        const response = await fetch(`/api/stocks/search?query=${encodeURIComponent(this.searchQuery)}`);
        if (response.ok) {
          this.searchResults = await response.json();
          if (this.searchResults.length === 0) {
            this.errorMessage = '沒有找到相關股票';
          }
        } else {
          this.errorMessage = '搜尋失敗，請稍後再試';
        }
      } catch (error) {
        this.errorMessage = '網路錯誤，請檢查連線';
        console.error('搜尋錯誤:', error);
      } finally {
        this.loading = false;
      }
    },

    async selectStock(symbol) {
      this.selectedStock = { symbol };
      this.currentQuote = null;
      this.stockInfo = null;
      this.errorMessage = '';
      
      // 同時獲取報價和公司資訊
      await Promise.all([
        this.getQuote(symbol),
        this.getStockInfo(symbol)
      ]);
    },

    async getQuote(symbol) {
      this.loading = true;
      try {
        const response = await fetch(`/api/stocks/quote/${symbol}`);
        if (response.ok) {
          this.currentQuote = await response.json();
        } else {
          this.errorMessage = '無法獲取股票報價';
        }
      } catch (error) {
        this.errorMessage = '獲取報價時發生錯誤';
        console.error('獲取報價錯誤:', error);
      } finally {
        this.loading = false;
      }
    },

    async getStockInfo(symbol) {
      try {
        const response = await fetch(`/api/stocks/info/${symbol}`);
        if (response.ok) {
          this.stockInfo = await response.json();
          this.selectedStock = { ...this.selectedStock, ...this.stockInfo };
        }
      } catch (error) {
        console.error('獲取股票資訊錯誤:', error);
      }
    },

    async refreshQuote() {
      if (this.selectedStock) {
        await this.getQuote(this.selectedStock.symbol);
      }
    },

    async syncToDatabase() {
      if (!this.selectedStock) return;
      
      this.loading = true;
      try {
        const response = await fetch(`/api/stocks/sync/${this.selectedStock.symbol}`, {
          method: 'POST'
        });
        if (response.ok) {
          this.errorMessage = '';
          alert('股票資料已成功同步到資料庫');
        } else {
          this.errorMessage = '同步失敗，請稍後再試';
        }
      } catch (error) {
        this.errorMessage = '同步時發生錯誤';
        console.error('同步錯誤:', error);
      } finally {
        this.loading = false;
      }
    },

    formatVolume(volume) {
      if (!volume) return 'N/A';
      if (volume >= 1000000) {
        return (volume / 1000000).toFixed(1) + 'M';
      } else if (volume >= 1000) {
        return (volume / 1000).toFixed(1) + 'K';
      }
      return volume.toLocaleString();
    },

    formatMarketCap(marketCap) {
      if (!marketCap) return 'N/A';
      if (marketCap >= 1000000000000) {
        return '$' + (marketCap / 1000000000000).toFixed(1) + 'T';
      } else if (marketCap >= 1000000000) {
        return '$' + (marketCap / 1000000000).toFixed(1) + 'B';
      } else if (marketCap >= 1000000) {
        return '$' + (marketCap / 1000000).toFixed(1) + 'M';
      }
      return '$' + marketCap.toLocaleString();
    }
  }
};
</script>

<style scoped>
.stock-search {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.search-section {
  margin-bottom: 30px;
}

.search-input-group {
  display: flex;
  gap: 10px;
  max-width: 600px;
}

.search-input {
  flex: 1;
  padding: 12px;
  border: 2px solid #ddd;
  border-radius: 6px;
  font-size: 16px;
}

.search-input:focus {
  outline: none;
  border-color: #007bff;
}

.search-button, .refresh-button, .sync-button {
  padding: 12px 24px;
  background-color: #007bff;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 16px;
}

.search-button:hover, .refresh-button:hover, .sync-button:hover {
  background-color: #0056b3;
}

.search-button:disabled, .refresh-button:disabled, .sync-button:disabled {
  background-color: #ccc;
  cursor: not-allowed;
}

.stock-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 15px;
  margin-top: 15px;
}

.stock-card {
  border: 1px solid #ddd;
  border-radius: 8px;
  padding: 15px;
  background-color: #f9f9f9;
}

.stock-card.clickable {
  cursor: pointer;
  transition: all 0.2s;
}

.stock-card.clickable:hover {
  background-color: #e9ecef;
  border-color: #007bff;
}

.stock-symbol {
  font-weight: bold;
  font-size: 18px;
  color: #007bff;
}

.stock-name {
  margin: 5px 0;
  color: #333;
}

.stock-exchange {
  font-size: 14px;
  color: #666;
}

.stock-details {
  margin-top: 30px;
  padding: 20px;
  border: 1px solid #ddd;
  border-radius: 8px;
  background-color: #f8f9fa;
}

.quote-section, .info-section {
  margin: 20px 0;
}

.quote-grid, .info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 15px;
  margin-top: 10px;
}

.quote-item, .info-item {
  display: flex;
  justify-content: space-between;
  padding: 8px;
  background-color: white;
  border-radius: 4px;
}

.label {
  font-weight: bold;
  color: #666;
}

.value {
  color: #333;
}

.value.price {
  font-size: 18px;
  font-weight: bold;
}

.positive {
  color: #28a745;
}

.negative {
  color: #dc3545;
}

.action-buttons {
  display: flex;
  gap: 10px;
  margin-top: 20px;
}

.sync-button {
  background-color: #28a745;
}

.sync-button:hover {
  background-color: #1e7e34;
}

.error-message {
  margin-top: 15px;
  padding: 10px;
  background-color: #f8d7da;
  color: #721c24;
  border: 1px solid #f5c6cb;
  border-radius: 4px;
}

h2, h3, h4 {
  color: #333;
}

a {
  color: #007bff;
  text-decoration: none;
}

a:hover {
  text-decoration: underline;
}
</style>
