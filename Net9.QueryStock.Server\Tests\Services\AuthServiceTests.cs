using Microsoft.Extensions.DependencyInjection;
using Net9.QueryStock.Server.DTOs;
using Net9.QueryStock.Server.Interfaces;
using Xunit;

namespace Net9.QueryStock.Server.Tests.Services
{
    /// <summary>
    /// 認證服務測試
    /// 測試用戶註冊、登入、JWT 權杖生成等功能
    /// </summary>
    public class AuthServiceTests : TestBase
    {
        private readonly IAuthService _authService;

        public AuthServiceTests()
        {
            _authService = ServiceProvider.GetRequiredService<IAuthService>();
        }

        [Fact]
        public async Task RegisterAsync_WithValidData_ReturnsSuccessAndJwtToken()
        {
            // Arrange
            await CleanupTestDataAsync();
            var registerRequest = new RegisterRequest
            {
                Username = "newuser",
                Password = "password123",
                ConfirmPassword = "password123",
                Email = "<EMAIL>"
            };

            // Act
            var result = await _authService.RegisterAsync(registerRequest);

            // Assert
            Assert.True(result.Success);
            Assert.NotNull(result.Token);
            Assert.NotNull(result.User);
            Assert.Equal("newuser", result.User.Username);
            Assert.Equal("<EMAIL>", result.User.Email);
            Assert.True(result.ExpiresAt > DateTimeOffset.UtcNow);
            
            // 驗證用戶已存儲到資料庫
            var storedUser = Context.Users.FirstOrDefault(u => u.Username == "newuser");
            Assert.NotNull(storedUser);
            Assert.True(storedUser.IsActive);
        }

        [Fact]
        public async Task RegisterAsync_WithDuplicateUsername_ReturnsFailure()
        {
            // Arrange
            await CleanupTestDataAsync();
            await CreateTestUserAsync("existinguser", "password123");
            
            var registerRequest = new RegisterRequest
            {
                Username = "existinguser",
                Password = "newpassword123",
                ConfirmPassword = "newpassword123",
                Email = "<EMAIL>"
            };

            // Act
            var result = await _authService.RegisterAsync(registerRequest);

            // Assert
            Assert.False(result.Success);
            Assert.Contains("用戶名稱已存在", result.Message);
            Assert.Null(result.Token);
        }

        [Fact]
        public async Task RegisterAsync_WithDuplicateEmail_ReturnsFailure()
        {
            // Arrange
            await CleanupTestDataAsync();
            await CreateTestUserAsync("user1", "password123", "<EMAIL>");
            
            var registerRequest = new RegisterRequest
            {
                Username = "user2",
                Password = "password123",
                ConfirmPassword = "password123",
                Email = "<EMAIL>"
            };

            // Act
            var result = await _authService.RegisterAsync(registerRequest);

            // Assert
            Assert.False(result.Success);
            Assert.Contains("電子郵件已被使用", result.Message);
            Assert.Null(result.Token);
        }

        [Fact]
        public async Task LoginAsync_WithValidCredentials_ReturnsSuccessAndJwtToken()
        {
            // Arrange
            await CleanupTestDataAsync();
            await CreateTestUserAsync("testuser", "password123", "<EMAIL>");
            
            var loginRequest = new LoginRequest
            {
                Username = "testuser",
                Password = "password123"
            };

            // Act
            var result = await _authService.LoginAsync(loginRequest);

            // Assert
            Assert.True(result.Success);
            Assert.NotNull(result.Token);
            Assert.NotNull(result.User);
            Assert.Equal("testuser", result.User.Username);
            Assert.True(result.ExpiresAt > DateTimeOffset.UtcNow);
            
            // 驗證最後登入時間已更新
            var user = Context.Users.FirstOrDefault(u => u.Username == "testuser");
            Assert.NotNull(user);
            Assert.NotNull(user.LastLoginAt);
        }

        [Fact]
        public async Task LoginAsync_WithInvalidUsername_ReturnsFailure()
        {
            // Arrange
            await CleanupTestDataAsync();
            var loginRequest = new LoginRequest
            {
                Username = "nonexistentuser",
                Password = "password123"
            };

            // Act
            var result = await _authService.LoginAsync(loginRequest);

            // Assert
            Assert.False(result.Success);
            Assert.Contains("用戶名稱或密碼錯誤", result.Message);
            Assert.Null(result.Token);
        }

        [Fact]
        public async Task LoginAsync_WithInvalidPassword_ReturnsFailure()
        {
            // Arrange
            await CleanupTestDataAsync();
            await CreateTestUserAsync("testuser", "correctpassword");
            
            var loginRequest = new LoginRequest
            {
                Username = "testuser",
                Password = "wrongpassword"
            };

            // Act
            var result = await _authService.LoginAsync(loginRequest);

            // Assert
            Assert.False(result.Success);
            Assert.Contains("用戶名稱或密碼錯誤", result.Message);
            Assert.Null(result.Token);
        }

        [Fact]
        public async Task LoginAsync_WithInactiveUser_ReturnsFailure()
        {
            // Arrange
            await CleanupTestDataAsync();
            var user = await CreateTestUserAsync("inactiveuser", "password123");
            user.IsActive = false;
            Context.Users.Update(user);
            await Context.SaveChangesAsync();
            
            var loginRequest = new LoginRequest
            {
                Username = "inactiveuser",
                Password = "password123"
            };

            // Act
            var result = await _authService.LoginAsync(loginRequest);

            // Assert
            Assert.False(result.Success);
            Assert.Contains("用戶名稱或密碼錯誤", result.Message);
            Assert.Null(result.Token);
        }

        [Fact]
        public async Task ValidateTokenAsync_WithValidToken_ReturnsUserDto()
        {
            // Arrange
            await CleanupTestDataAsync();
            var user = await CreateTestUserAsync("testuser", "password123");
            var (token, _) = _authService.GenerateJwtToken(user);

            // Act
            var result = await _authService.ValidateTokenAsync(token);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(user.Id, result.Id);
            Assert.Equal("testuser", result.Username);
        }

        [Fact]
        public async Task ValidateTokenAsync_WithInvalidToken_ReturnsNull()
        {
            // Arrange
            var invalidToken = "invalid.jwt.token";

            // Act
            var result = await _authService.ValidateTokenAsync(invalidToken);

            // Assert
            Assert.Null(result);
        }

        [Fact]
        public async Task ChangePasswordAsync_WithValidCurrentPassword_ReturnsSuccess()
        {
            // Arrange
            await CleanupTestDataAsync();
            var user = await CreateTestUserAsync("testuser", "oldpassword123");
            
            var changePasswordRequest = new ChangePasswordRequest
            {
                CurrentPassword = "oldpassword123",
                NewPassword = "newpassword123",
                ConfirmNewPassword = "newpassword123"
            };

            // Act
            var result = await _authService.ChangePasswordAsync(user.Id, changePasswordRequest);

            // Assert
            Assert.True(result.Success);
            
            // 驗證新密碼可以登入
            var loginRequest = new LoginRequest
            {
                Username = "testuser",
                Password = "newpassword123"
            };
            var loginResult = await _authService.LoginAsync(loginRequest);
            Assert.True(loginResult.Success);
        }

        [Fact]
        public async Task ChangePasswordAsync_WithInvalidCurrentPassword_ReturnsFailure()
        {
            // Arrange
            await CleanupTestDataAsync();
            var user = await CreateTestUserAsync("testuser", "correctpassword");
            
            var changePasswordRequest = new ChangePasswordRequest
            {
                CurrentPassword = "wrongpassword",
                NewPassword = "newpassword123",
                ConfirmNewPassword = "newpassword123"
            };

            // Act
            var result = await _authService.ChangePasswordAsync(user.Id, changePasswordRequest);

            // Assert
            Assert.False(result.Success);
            Assert.Contains("當前密碼錯誤", result.Message);
        }

        [Fact]
        public void GenerateJwtToken_WithValidUser_ReturnsValidToken()
        {
            // Arrange
            var user = new Models.User
            {
                Id = Guid.NewGuid(),
                Username = "testuser",
                Email = "<EMAIL>",
                Role = "User"
            };

            // Act
            var (token, expiresAt) = _authService.GenerateJwtToken(user);

            // Assert
            Assert.NotNull(token);
            Assert.True(expiresAt > DateTimeOffset.UtcNow);
            Assert.True(expiresAt <= DateTimeOffset.UtcNow.AddHours(25)); // 應該在 24 小時左右
        }

        [Fact]
        public void HashPassword_WithSamePassword_ProducesDifferentHashes()
        {
            // Arrange
            var password = "testpassword123";

            // Act
            var (hash1, salt1) = _authService.HashPassword(password);
            var (hash2, salt2) = _authService.HashPassword(password);

            // Assert
            Assert.NotEqual(hash1, hash2); // 不同的鹽值應該產生不同的雜湊
            Assert.NotEqual(salt1, salt2);
        }

        [Fact]
        public void VerifyPassword_WithCorrectPassword_ReturnsTrue()
        {
            // Arrange
            var password = "testpassword123";
            var (hash, salt) = _authService.HashPassword(password);

            // Act
            var result = _authService.VerifyPassword(password, hash, salt);

            // Assert
            Assert.True(result);
        }

        [Fact]
        public void VerifyPassword_WithIncorrectPassword_ReturnsFalse()
        {
            // Arrange
            var correctPassword = "testpassword123";
            var wrongPassword = "wrongpassword";
            var (hash, salt) = _authService.HashPassword(correctPassword);

            // Act
            var result = _authService.VerifyPassword(wrongPassword, hash, salt);

            // Assert
            Assert.False(result);
        }
    }
}
