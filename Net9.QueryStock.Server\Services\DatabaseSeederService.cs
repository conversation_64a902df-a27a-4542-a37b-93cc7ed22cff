using Microsoft.EntityFrameworkCore;
using Net9.QueryStock.Server.Data;
using Net9.QueryStock.Server.Interfaces;
using Net9.QueryStock.Server.Models;

namespace Net9.QueryStock.Server.Services
{
    /// <summary>
    /// 資料庫種子資料服務
    /// </summary>
    public class DatabaseSeederService
    {
        private readonly Net9QueryStockServerContext _context;
        private readonly IApiKeyEncryptionService _encryptionService;
        private readonly ILogger<DatabaseSeederService> _logger;

        public DatabaseSeederService(
            Net9QueryStockServerContext context,
            IApiKeyEncryptionService encryptionService,
            ILogger<DatabaseSeederService> logger)
        {
            _context = context;
            _encryptionService = encryptionService;
            _logger = logger;
        }

        /// <summary>
        /// 初始化資料庫種子資料
        /// </summary>
        public async Task SeedAsync()
        {
            try
            {
                await SeedApiKeyProvidersAsync();
                await _context.SaveChangesAsync();
                _logger.LogInformation("資料庫種子資料初始化完成");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "初始化資料庫種子資料時發生錯誤");
                throw;
            }
        }

        /// <summary>
        /// 初始化 API 金鑰提供者
        /// </summary>
        private async Task SeedApiKeyProvidersAsync()
        {
            // 檢查是否已存在 Finnhub 提供者
            var finnhubExists = await _context.ApiKeyProviders
                .AnyAsync(p => p.Name == "Finnhub");

            if (!finnhubExists)
            {
                var finnhubProvider = new ApiKeyProvider
                {
                    Id = Guid.NewGuid(),
                    Name = "Finnhub",
                    DisplayName = "Finnhub.io",
                    Description = "Finnhub.io 提供即時股票資料、公司資訊和市場資料的 API 服務",
                    BaseUrl = "https://finnhub.io/api/v1",
                    RateLimitPerMinute = 60,
                    RateLimitPerDay = 1000,
                    IsEnabled = true,
                    Priority = 1,
                    CreatedAt = DateTimeOffset.UtcNow,
                    UpdatedAt = DateTimeOffset.UtcNow
                };

                _context.ApiKeyProviders.Add(finnhubProvider);
                _logger.LogInformation("已新增 Finnhub 服務提供者");
            }

            // 檢查是否已存在 AlphaVantage 提供者
            var alphaVantageExists = await _context.ApiKeyProviders
                .AnyAsync(p => p.Name == "AlphaVantage");

            if (!alphaVantageExists)
            {
                var alphaVantageProvider = new ApiKeyProvider
                {
                    Id = Guid.NewGuid(),
                    Name = "AlphaVantage",
                    DisplayName = "Alpha Vantage",
                    Description = "Alpha Vantage 提供股票、外匯和加密貨幣的即時和歷史資料 API",
                    BaseUrl = "https://www.alphavantage.co/query",
                    RateLimitPerMinute = 5,
                    RateLimitPerDay = 500,
                    IsEnabled = false, // 預設停用，等待實作
                    Priority = 2,
                    CreatedAt = DateTimeOffset.UtcNow,
                    UpdatedAt = DateTimeOffset.UtcNow
                };

                _context.ApiKeyProviders.Add(alphaVantageProvider);
                _logger.LogInformation("已新增 AlphaVantage 服務提供者");
            }

            // 檢查是否已存在 YahooFinance 提供者
            var yahooFinanceExists = await _context.ApiKeyProviders
                .AnyAsync(p => p.Name == "YahooFinance");

            if (!yahooFinanceExists)
            {
                var yahooFinanceProvider = new ApiKeyProvider
                {
                    Id = Guid.NewGuid(),
                    Name = "YahooFinance",
                    DisplayName = "Yahoo Finance",
                    Description = "Yahoo Finance 提供股票、指數和市場資料的 API 服務",
                    BaseUrl = "https://query1.finance.yahoo.com/v8/finance/chart",
                    RateLimitPerMinute = 100,
                    RateLimitPerDay = 2000,
                    IsEnabled = false, // 預設停用，等待實作
                    Priority = 3,
                    CreatedAt = DateTimeOffset.UtcNow,
                    UpdatedAt = DateTimeOffset.UtcNow
                };

                _context.ApiKeyProviders.Add(yahooFinanceProvider);
                _logger.LogInformation("已新增 YahooFinance 服務提供者");
            }
        }

        /// <summary>
        /// 新增示例 API 金鑰（僅用於開發測試）
        /// </summary>
        public async Task AddSampleApiKeyAsync(string providerName, string keyName, string apiKey)
        {
            try
            {
                var provider = await _context.ApiKeyProviders
                    .FirstOrDefaultAsync(p => p.Name == providerName);

                if (provider == null)
                {
                    _logger.LogWarning("找不到服務提供者: {ProviderName}", providerName);
                    return;
                }

                // 檢查是否已存在相同名稱的金鑰
                var existingKey = await _context.ApiKeys
                    .AnyAsync(k => k.ProviderId == provider.Id && k.Name == keyName);

                if (existingKey)
                {
                    _logger.LogInformation("金鑰 {KeyName} 已存在於提供者 {ProviderName}", keyName, providerName);
                    return;
                }

                var encryptedKey = _encryptionService.EncryptKey(apiKey);

                var newApiKey = new ApiKey
                {
                    Id = Guid.NewGuid(),
                    ProviderId = provider.Id,
                    Name = keyName,
                    EncryptedKey = encryptedKey,
                    Status = ApiKeyStatus.Active,
                    Priority = 1,
                    CreatedAt = DateTimeOffset.UtcNow,
                    UpdatedAt = DateTimeOffset.UtcNow,
                    CurrentMinuteStartTime = DateTimeOffset.UtcNow
                };

                _context.ApiKeys.Add(newApiKey);
                await _context.SaveChangesAsync();

                _logger.LogInformation("已新增 API 金鑰 {KeyName} 到提供者 {ProviderName}", keyName, providerName);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "新增示例 API 金鑰時發生錯誤");
                throw;
            }
        }
    }
}
