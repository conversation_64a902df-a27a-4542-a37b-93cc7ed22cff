using Microsoft.EntityFrameworkCore;
using Microsoft.IdentityModel.Tokens;
using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using System.Security.Cryptography;
using System.Text;
using Net9.QueryStock.Server.Data;
using Net9.QueryStock.Server.DTOs;
using Net9.QueryStock.Server.Interfaces;
using Net9.QueryStock.Server.Models;

namespace Net9.QueryStock.Server.Services
{
    /// <summary>
    /// 認證服務實作
    /// </summary>
    public class AuthService : IAuthService
    {
        private readonly Net9QueryStockServerContext _context;
        private readonly IConfiguration _configuration;
        private readonly ILogger<AuthService> _logger;

        public AuthService(
            Net9QueryStockServerContext context,
            IConfiguration configuration,
            ILogger<AuthService> logger)
        {
            _context = context;
            _configuration = configuration;
            _logger = logger;
        }

        /// <summary>
        /// 用戶註冊
        /// </summary>
        public async Task<AuthResponse> RegisterAsync(RegisterRequest request, CancellationToken cancellationToken = default)
        {
            try
            {
                // 檢查用戶名是否已存在
                var existingUser = await _context.Users
                    .FirstOrDefaultAsync(u => u.Username == request.Username, cancellationToken);

                if (existingUser != null)
                {
                    return new AuthResponse
                    {
                        Success = false,
                        Message = "用戶名稱已存在"
                    };
                }

                // 檢查電子郵件是否已存在（如果提供）
                if (!string.IsNullOrEmpty(request.Email))
                {
                    var existingEmail = await _context.Users
                        .FirstOrDefaultAsync(u => u.Email == request.Email, cancellationToken);

                    if (existingEmail != null)
                    {
                        return new AuthResponse
                        {
                            Success = false,
                            Message = "電子郵件已被使用"
                        };
                    }
                }

                // 雜湊密碼
                var (hash, salt) = HashPassword(request.Password);

                // 建立新用戶
                var user = new User
                {
                    Id = Guid.NewGuid(),
                    Username = request.Username,
                    Email = request.Email,
                    PasswordHash = hash,
                    Salt = salt,
                    Role = "User",
                    IsActive = true,
                    CreatedAt = DateTimeOffset.UtcNow,
                    UpdatedAt = DateTimeOffset.UtcNow
                };

                _context.Users.Add(user);
                await _context.SaveChangesAsync(cancellationToken);

                // 生成 JWT 權杖
                var (token, expiresAt) = GenerateJwtToken(user);

                _logger.LogInformation("用戶註冊成功: {Username}", request.Username);

                return new AuthResponse
                {
                    Success = true,
                    Message = "註冊成功",
                    Token = token,
                    ExpiresAt = expiresAt,
                    User = MapToUserDto(user)
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "用戶註冊時發生錯誤: {Username}", request.Username);
                return new AuthResponse
                {
                    Success = false,
                    Message = "註冊時發生錯誤，請稍後再試"
                };
            }
        }

        /// <summary>
        /// 用戶登入
        /// </summary>
        public async Task<AuthResponse> LoginAsync(LoginRequest request, CancellationToken cancellationToken = default)
        {
            try
            {
                var user = await _context.Users
                    .FirstOrDefaultAsync(u => u.Username == request.Username, cancellationToken);

                if (user == null || !user.IsActive)
                {
                    return new AuthResponse
                    {
                        Success = false,
                        Message = "用戶名稱或密碼錯誤"
                    };
                }

                // 驗證密碼
                if (!VerifyPassword(request.Password, user.PasswordHash, user.Salt))
                {
                    return new AuthResponse
                    {
                        Success = false,
                        Message = "用戶名稱或密碼錯誤"
                    };
                }

                // 更新最後登入時間
                user.LastLoginAt = DateTimeOffset.UtcNow;
                user.UpdatedAt = DateTimeOffset.UtcNow;
                await _context.SaveChangesAsync(cancellationToken);

                // 生成 JWT 權杖
                var (token, expiresAt) = GenerateJwtToken(user);

                _logger.LogInformation("用戶登入成功: {Username}", request.Username);

                return new AuthResponse
                {
                    Success = true,
                    Message = "登入成功",
                    Token = token,
                    ExpiresAt = expiresAt,
                    User = MapToUserDto(user)
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "用戶登入時發生錯誤: {Username}", request.Username);
                return new AuthResponse
                {
                    Success = false,
                    Message = "登入時發生錯誤，請稍後再試"
                };
            }
        }

        /// <summary>
        /// 驗證權杖
        /// </summary>
        public async Task<UserDto?> ValidateTokenAsync(string token)
        {
            try
            {
                var tokenHandler = new JwtSecurityTokenHandler();
                var key = Encoding.UTF8.GetBytes(_configuration["Jwt:Key"] ?? "DefaultSecretKey123456789012345678901234567890");

                var validationParameters = new TokenValidationParameters
                {
                    ValidateIssuerSigningKey = true,
                    IssuerSigningKey = new SymmetricSecurityKey(key),
                    ValidateIssuer = true,
                    ValidIssuer = _configuration["Jwt:Issuer"],
                    ValidateAudience = true,
                    ValidAudience = _configuration["Jwt:Audience"],
                    ValidateLifetime = true,
                    ClockSkew = TimeSpan.Zero
                };

                var principal = tokenHandler.ValidateToken(token, validationParameters, out var validatedToken);
                var userIdClaim = principal.FindFirst(ClaimTypes.NameIdentifier);

                if (userIdClaim != null && Guid.TryParse(userIdClaim.Value, out var userId))
                {
                    return await GetUserByIdAsync(userId);
                }

                return null;
            }
            catch
            {
                return null;
            }
        }

        /// <summary>
        /// 根據用戶 ID 獲取用戶資訊
        /// </summary>
        public async Task<UserDto?> GetUserByIdAsync(Guid userId, CancellationToken cancellationToken = default)
        {
            try
            {
                var user = await _context.Users
                    .FirstOrDefaultAsync(u => u.Id == userId && u.IsActive, cancellationToken);

                return user != null ? MapToUserDto(user) : null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "獲取用戶資訊時發生錯誤: {UserId}", userId);
                return null;
            }
        }

        /// <summary>
        /// 修改密碼
        /// </summary>
        public async Task<AuthResponse> ChangePasswordAsync(Guid userId, ChangePasswordRequest request, CancellationToken cancellationToken = default)
        {
            try
            {
                var user = await _context.Users
                    .FirstOrDefaultAsync(u => u.Id == userId && u.IsActive, cancellationToken);

                if (user == null)
                {
                    return new AuthResponse
                    {
                        Success = false,
                        Message = "找不到用戶"
                    };
                }

                // 驗證當前密碼
                if (!VerifyPassword(request.CurrentPassword, user.PasswordHash, user.Salt))
                {
                    return new AuthResponse
                    {
                        Success = false,
                        Message = "當前密碼錯誤"
                    };
                }

                // 雜湊新密碼
                var (hash, salt) = HashPassword(request.NewPassword);

                // 更新密碼
                user.PasswordHash = hash;
                user.Salt = salt;
                user.UpdatedAt = DateTimeOffset.UtcNow;

                await _context.SaveChangesAsync(cancellationToken);

                _logger.LogInformation("用戶修改密碼成功: {UserId}", userId);

                return new AuthResponse
                {
                    Success = true,
                    Message = "密碼修改成功"
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "修改密碼時發生錯誤: {UserId}", userId);
                return new AuthResponse
                {
                    Success = false,
                    Message = "修改密碼時發生錯誤，請稍後再試"
                };
            }
        }

        /// <summary>
        /// 生成 JWT 權杖
        /// </summary>
        public (string Token, DateTimeOffset ExpiresAt) GenerateJwtToken(User user)
        {
            var key = Encoding.UTF8.GetBytes(_configuration["Jwt:Key"] ?? "DefaultSecretKey123456789012345678901234567890");
            var expiresAt = DateTimeOffset.UtcNow.AddHours(24); // 24 小時過期

            var claims = new[]
            {
                new Claim(ClaimTypes.NameIdentifier, user.Id.ToString()),
                new Claim(ClaimTypes.Name, user.Username),
                new Claim(ClaimTypes.Role, user.Role),
                new Claim("email", user.Email ?? string.Empty)
            };

            var tokenDescriptor = new SecurityTokenDescriptor
            {
                Subject = new ClaimsIdentity(claims),
                Expires = expiresAt.DateTime,
                Issuer = _configuration["Jwt:Issuer"],
                Audience = _configuration["Jwt:Audience"],
                SigningCredentials = new SigningCredentials(new SymmetricSecurityKey(key), SecurityAlgorithms.HmacSha256)
            };

            var tokenHandler = new JwtSecurityTokenHandler();
            var token = tokenHandler.CreateToken(tokenDescriptor);

            return (tokenHandler.WriteToken(token), expiresAt);
        }

        /// <summary>
        /// 驗證密碼
        /// </summary>
        public bool VerifyPassword(string password, string hash, string salt)
        {
            var (computedHash, _) = HashPassword(password, salt);
            return computedHash == hash;
        }

        /// <summary>
        /// 雜湊密碼
        /// </summary>
        public (string Hash, string Salt) HashPassword(string password)
        {
            var salt = GenerateSalt();
            return HashPassword(password, salt);
        }

        /// <summary>
        /// 使用指定鹽值雜湊密碼
        /// </summary>
        private (string Hash, string Salt) HashPassword(string password, string salt)
        {
            using var pbkdf2 = new Rfc2898DeriveBytes(password, Encoding.UTF8.GetBytes(salt), 10000, HashAlgorithmName.SHA256);
            var hash = Convert.ToBase64String(pbkdf2.GetBytes(32));
            return (hash, salt);
        }

        /// <summary>
        /// 生成隨機鹽值
        /// </summary>
        private string GenerateSalt()
        {
            var saltBytes = new byte[32];
            using var rng = RandomNumberGenerator.Create();
            rng.GetBytes(saltBytes);
            return Convert.ToBase64String(saltBytes);
        }

        /// <summary>
        /// 將 User 實體映射為 UserDto
        /// </summary>
        private UserDto MapToUserDto(User user)
        {
            return new UserDto
            {
                Id = user.Id,
                Username = user.Username,
                Email = user.Email,
                Role = user.Role,
                IsActive = user.IsActive,
                LastLoginAt = user.LastLoginAt,
                CreatedAt = user.CreatedAt
            };
        }
    }
}
