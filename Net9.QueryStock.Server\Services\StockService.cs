using Microsoft.EntityFrameworkCore;
using Net9.QueryStock.Server.Data;
using Net9.QueryStock.Server.DTOs;
using Net9.QueryStock.Server.Interfaces;
using Net9.QueryStock.Server.Models;

namespace Net9.QueryStock.Server.Services
{
    /// <summary>
    /// 股票服務實作
    /// </summary>
    public class StockService : IStockService
    {
        private readonly Net9QueryStockServerContext _context;
        private readonly IEnumerable<IStockDataProvider> _dataProviders;
        private readonly ICacheService _cacheService;
        private readonly ILogger<StockService> _logger;

        public StockService(
            Net9QueryStockServerContext context,
            IEnumerable<IStockDataProvider> dataProviders,
            ICacheService cacheService,
            ILogger<StockService> logger)
        {
            _context = context;
            _dataProviders = dataProviders;
            _cacheService = cacheService;
            _logger = logger;
        }

        /// <summary>
        /// 獲取可用的資料提供者
        /// </summary>
        private IStockDataProvider? GetAvailableProvider()
        {
            return _dataProviders.FirstOrDefault(p => p.IsAvailable);
        }

        /// <summary>
        /// 獲取即時股票報價
        /// </summary>
        public async Task<StockQuoteDto?> GetRealTimeQuoteAsync(string symbol, CancellationToken cancellationToken = default)
        {
            try
            {
                var cacheKey = $"quote:{symbol.ToUpper()}";
                
                // 先從快取獲取
                var cachedQuote = await _cacheService.GetAsync<StockQuoteDto>(cacheKey, cancellationToken);
                if (cachedQuote != null)
                {
                    _logger.LogDebug("從快取獲取 {Symbol} 的報價", symbol);
                    return cachedQuote;
                }
                
                // 從資料提供者獲取
                var provider = GetAvailableProvider();
                if (provider == null)
                {
                    _logger.LogWarning("沒有可用的資料提供者");
                    return null;
                }
                
                var quote = await provider.GetRealTimeQuoteAsync(symbol, cancellationToken);
                if (quote != null)
                {
                    // 快取 30 秒
                    await _cacheService.SetAsync(cacheKey, quote, TimeSpan.FromSeconds(30), cancellationToken);
                    _logger.LogInformation("成功獲取 {Symbol} 的即時報價", symbol);
                }
                
                return quote;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "獲取 {Symbol} 即時報價時發生錯誤", symbol);
                return null;
            }
        }

        /// <summary>
        /// 獲取股票詳細資訊
        /// </summary>
        public async Task<StockInfoDto?> GetStockInfoAsync(string symbol, CancellationToken cancellationToken = default)
        {
            try
            {
                var cacheKey = $"info:{symbol.ToUpper()}";
                
                // 先從快取獲取
                var cachedInfo = await _cacheService.GetAsync<StockInfoDto>(cacheKey, cancellationToken);
                if (cachedInfo != null)
                {
                    _logger.LogDebug("從快取獲取 {Symbol} 的詳細資訊", symbol);
                    return cachedInfo;
                }
                
                // 從資料提供者獲取
                var provider = GetAvailableProvider();
                if (provider == null)
                {
                    _logger.LogWarning("沒有可用的資料提供者");
                    return null;
                }
                
                var info = await provider.GetStockInfoAsync(symbol, cancellationToken);
                if (info != null)
                {
                    // 快取 1 小時
                    await _cacheService.SetAsync(cacheKey, info, TimeSpan.FromHours(1), cancellationToken);
                    _logger.LogInformation("成功獲取 {Symbol} 的詳細資訊", symbol);
                }
                
                return info;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "獲取 {Symbol} 詳細資訊時發生錯誤", symbol);
                return null;
            }
        }

        /// <summary>
        /// 獲取歷史價格資料
        /// </summary>
        public async Task<IEnumerable<StockPriceDto>> GetHistoricalPricesAsync(
            string symbol, 
            DateOnly fromDate, 
            DateOnly toDate, 
            CancellationToken cancellationToken = default)
        {
            try
            {
                var cacheKey = $"history:{symbol.ToUpper()}:{fromDate:yyyy-MM-dd}:{toDate:yyyy-MM-dd}";
                
                // 先從快取獲取
                var cachedPrices = await _cacheService.GetAsync<List<StockPriceDto>>(cacheKey, cancellationToken);
                if (cachedPrices != null)
                {
                    _logger.LogDebug("從快取獲取 {Symbol} 的歷史價格", symbol);
                    return cachedPrices;
                }
                
                // 從資料提供者獲取
                var provider = GetAvailableProvider();
                if (provider == null)
                {
                    _logger.LogWarning("沒有可用的資料提供者");
                    return Enumerable.Empty<StockPriceDto>();
                }
                
                var prices = await provider.GetHistoricalPricesAsync(symbol, fromDate, toDate, cancellationToken);
                var priceList = prices.ToList();
                
                if (priceList.Any())
                {
                    // 快取 4 小時
                    await _cacheService.SetAsync(cacheKey, priceList, TimeSpan.FromHours(4), cancellationToken);
                    _logger.LogInformation("成功獲取 {Symbol} 的歷史價格，共 {Count} 筆", symbol, priceList.Count);
                }
                
                return priceList;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "獲取 {Symbol} 歷史價格時發生錯誤", symbol);
                return Enumerable.Empty<StockPriceDto>();
            }
        }

        /// <summary>
        /// 搜尋股票
        /// </summary>
        public async Task<IEnumerable<StockInfoDto>> SearchStocksAsync(string query, CancellationToken cancellationToken = default)
        {
            try
            {
                var cacheKey = $"search:{query.ToLower()}";
                
                // 先從快取獲取
                var cachedResults = await _cacheService.GetAsync<List<StockInfoDto>>(cacheKey, cancellationToken);
                if (cachedResults != null)
                {
                    _logger.LogDebug("從快取獲取搜尋結果: {Query}", query);
                    return cachedResults;
                }
                
                // 從資料提供者獲取
                var provider = GetAvailableProvider();
                if (provider == null)
                {
                    _logger.LogWarning("沒有可用的資料提供者");
                    return Enumerable.Empty<StockInfoDto>();
                }
                
                var results = await provider.SearchStocksAsync(query, cancellationToken);
                var resultList = results.ToList();
                
                if (resultList.Any())
                {
                    // 快取 10 分鐘
                    await _cacheService.SetAsync(cacheKey, resultList, TimeSpan.FromMinutes(10), cancellationToken);
                    _logger.LogInformation("搜尋 {Query} 找到 {Count} 個結果", query, resultList.Count);
                }
                
                return resultList;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "搜尋股票時發生錯誤: {Query}", query);
                return Enumerable.Empty<StockInfoDto>();
            }
        }

        /// <summary>
        /// 批量獲取即時報價
        /// </summary>
        public async Task<Dictionary<string, StockQuoteDto>> GetBatchQuotesAsync(
            IEnumerable<string> symbols,
            CancellationToken cancellationToken = default)
        {
            try
            {
                var provider = GetAvailableProvider();
                if (provider == null)
                {
                    _logger.LogWarning("沒有可用的資料提供者");
                    return new Dictionary<string, StockQuoteDto>();
                }

                var symbolList = symbols.ToList();
                _logger.LogInformation("批量獲取 {Count} 個股票的即時報價", symbolList.Count);

                return await provider.GetBatchQuotesAsync(symbolList, cancellationToken);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "批量獲取即時報價時發生錯誤");
                return new Dictionary<string, StockQuoteDto>();
            }
        }

        /// <summary>
        /// 同步股票資料到資料庫
        /// </summary>
        public async Task<bool> SyncStockDataAsync(string symbol, CancellationToken cancellationToken = default)
        {
            try
            {
                _logger.LogInformation("開始同步 {Symbol} 的股票資料", symbol);

                var provider = GetAvailableProvider();
                if (provider == null)
                {
                    _logger.LogWarning("沒有可用的資料提供者");
                    return false;
                }

                // 獲取股票詳細資訊
                var stockInfo = await provider.GetStockInfoAsync(symbol, cancellationToken);
                if (stockInfo == null)
                {
                    _logger.LogWarning("無法獲取 {Symbol} 的股票資訊", symbol);
                    return false;
                }

                // 檢查資料庫中是否已存在該股票
                var existingStock = await _context.Stock
                    .FirstOrDefaultAsync(s => s.Symbol == symbol.ToUpper(), cancellationToken);

                if (existingStock == null)
                {
                    // 新增股票
                    var newStock = new Stock
                    {
                        Id = Guid.NewGuid(),
                        Symbol = symbol.ToUpper(),
                        Name = stockInfo.Name,
                        Exchange = stockInfo.Exchange,
                        Currency = stockInfo.Currency,
                        StockType = stockInfo.StockType,
                        Industry = stockInfo.Industry,
                        Country = stockInfo.Country,
                        IsActive = true,
                        CreatedAt = DateTimeOffset.UtcNow,
                        UpdatedAt = DateTimeOffset.UtcNow
                    };

                    _context.Stock.Add(newStock);
                    existingStock = newStock;
                }
                else
                {
                    // 更新現有股票資訊
                    existingStock.Name = stockInfo.Name;
                    existingStock.Exchange = stockInfo.Exchange;
                    existingStock.Currency = stockInfo.Currency;
                    existingStock.StockType = stockInfo.StockType;
                    existingStock.Industry = stockInfo.Industry;
                    existingStock.Country = stockInfo.Country;
                    existingStock.UpdatedAt = DateTimeOffset.UtcNow;
                }

                // 同步股票詳細資訊
                await SyncStockInfoAsync(existingStock.Id, stockInfo, cancellationToken);

                // 同步最新價格
                await SyncLatestPriceAsync(existingStock.Id, symbol, cancellationToken);

                await _context.SaveChangesAsync(cancellationToken);

                _logger.LogInformation("成功同步 {Symbol} 的股票資料", symbol);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "同步 {Symbol} 股票資料時發生錯誤", symbol);
                return false;
            }
        }

        /// <summary>
        /// 同步股票詳細資訊
        /// </summary>
        private async Task SyncStockInfoAsync(Guid stockId, StockInfoDto stockInfo, CancellationToken cancellationToken)
        {
            var existingInfo = await _context.Set<StockInfo>()
                .FirstOrDefaultAsync(si => si.StockId == stockId, cancellationToken);

            if (existingInfo == null)
            {
                var newInfo = new StockInfo
                {
                    Id = Guid.NewGuid(),
                    StockId = stockId,
                    MarketCapitalization = stockInfo.MarketCapitalization,
                    PeRatio = stockInfo.PeRatio,
                    PbRatio = stockInfo.PbRatio,
                    DividendYield = stockInfo.DividendYield,
                    EarningsPerShare = stockInfo.EarningsPerShare,
                    Week52High = stockInfo.Week52High,
                    Week52Low = stockInfo.Week52Low,
                    Description = stockInfo.Description,
                    Website = stockInfo.Website,
                    Headquarters = stockInfo.Headquarters,
                    EmployeeCount = stockInfo.EmployeeCount,
                    FoundedDate = stockInfo.FoundedDate,
                    IpoDate = stockInfo.IpoDate,
                    UpdatedAt = DateTimeOffset.UtcNow
                };

                _context.Set<StockInfo>().Add(newInfo);
            }
            else
            {
                existingInfo.MarketCapitalization = stockInfo.MarketCapitalization;
                existingInfo.PeRatio = stockInfo.PeRatio;
                existingInfo.PbRatio = stockInfo.PbRatio;
                existingInfo.DividendYield = stockInfo.DividendYield;
                existingInfo.EarningsPerShare = stockInfo.EarningsPerShare;
                existingInfo.Week52High = stockInfo.Week52High;
                existingInfo.Week52Low = stockInfo.Week52Low;
                existingInfo.Description = stockInfo.Description;
                existingInfo.Website = stockInfo.Website;
                existingInfo.Headquarters = stockInfo.Headquarters;
                existingInfo.EmployeeCount = stockInfo.EmployeeCount;
                existingInfo.FoundedDate = stockInfo.FoundedDate;
                existingInfo.IpoDate = stockInfo.IpoDate;
                existingInfo.UpdatedAt = DateTimeOffset.UtcNow;
            }
        }

        /// <summary>
        /// 同步最新價格
        /// </summary>
        private async Task SyncLatestPriceAsync(Guid stockId, string symbol, CancellationToken cancellationToken)
        {
            var provider = GetAvailableProvider();
            if (provider == null) return;

            var quote = await provider.GetRealTimeQuoteAsync(symbol, cancellationToken);
            if (quote == null) return;

            var today = DateOnly.FromDateTime(DateTime.Today);
            var existingPrice = await _context.Set<StockPrice>()
                .FirstOrDefaultAsync(sp => sp.StockId == stockId && sp.PriceDate == today, cancellationToken);

            if (existingPrice == null)
            {
                var newPrice = new StockPrice
                {
                    Id = Guid.NewGuid(),
                    StockId = stockId,
                    OpenPrice = quote.OpenPrice ?? quote.CurrentPrice,
                    HighPrice = quote.HighPrice ?? quote.CurrentPrice,
                    LowPrice = quote.LowPrice ?? quote.CurrentPrice,
                    ClosePrice = quote.CurrentPrice,
                    Volume = quote.Volume ?? 0,
                    PriceDate = today,
                    DataSource = quote.DataSource,
                    CreatedAt = DateTimeOffset.UtcNow
                };

                _context.Set<StockPrice>().Add(newPrice);
            }
            else
            {
                existingPrice.HighPrice = Math.Max(existingPrice.HighPrice, quote.HighPrice ?? quote.CurrentPrice);
                existingPrice.LowPrice = Math.Min(existingPrice.LowPrice, quote.LowPrice ?? quote.CurrentPrice);
                existingPrice.ClosePrice = quote.CurrentPrice;
                existingPrice.Volume = quote.Volume ?? existingPrice.Volume;
            }
        }

        /// <summary>
        /// 獲取資料庫中的股票列表
        /// </summary>
        public async Task<IEnumerable<Stock>> GetStoredStocksAsync(CancellationToken cancellationToken = default)
        {
            return await _context.Stock
                .Where(s => s.IsActive)
                .OrderBy(s => s.Symbol)
                .ToListAsync(cancellationToken);
        }

        /// <summary>
        /// 根據ID獲取股票
        /// </summary>
        public async Task<Stock?> GetStockByIdAsync(Guid id, CancellationToken cancellationToken = default)
        {
            return await _context.Stock
                .Include(s => s.StockInfo)
                .Include(s => s.StockPrices.OrderByDescending(sp => sp.PriceDate).Take(30))
                .FirstOrDefaultAsync(s => s.Id == id, cancellationToken);
        }

        /// <summary>
        /// 根據代號獲取股票
        /// </summary>
        public async Task<Stock?> GetStockBySymbolAsync(string symbol, CancellationToken cancellationToken = default)
        {
            return await _context.Stock
                .Include(s => s.StockInfo)
                .Include(s => s.StockPrices.OrderByDescending(sp => sp.PriceDate).Take(30))
                .FirstOrDefaultAsync(s => s.Symbol == symbol.ToUpper(), cancellationToken);
        }
    }
}
