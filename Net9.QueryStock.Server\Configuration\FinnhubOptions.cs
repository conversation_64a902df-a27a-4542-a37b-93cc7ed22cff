namespace Net9.QueryStock.Server.Configuration
{
    /// <summary>
    /// Finnhub API 設定選項
    /// </summary>
    public class FinnhubOptions
    {
        /// <summary>
        /// 設定區段名稱
        /// </summary>
        public const string SectionName = "Finnhub";
        
        /// <summary>
        /// API 金鑰
        /// </summary>
        public string ApiKey { get; set; } = string.Empty;
        
        /// <summary>
        /// API 基礎 URL
        /// </summary>
        public string BaseUrl { get; set; } = "https://finnhub.io/api/v1";
        
        /// <summary>
        /// HTTP 客戶端超時時間（秒）
        /// </summary>
        public int TimeoutSeconds { get; set; } = 30;
        
        /// <summary>
        /// 每分鐘最大請求數
        /// </summary>
        public int RateLimitPerMinute { get; set; } = 60;
        
        /// <summary>
        /// 是否啟用
        /// </summary>
        public bool IsEnabled { get; set; } = true;
    }
}
