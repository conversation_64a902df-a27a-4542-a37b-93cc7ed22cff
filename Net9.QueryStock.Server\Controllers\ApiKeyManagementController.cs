using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Net9.QueryStock.Server.Data;
using Net9.QueryStock.Server.DTOs;
using Net9.QueryStock.Server.Interfaces;
using Net9.QueryStock.Server.Models;

namespace Net9.QueryStock.Server.Controllers
{
    /// <summary>
    /// API 金鑰管理控制器
    /// </summary>
    [Route("api/[controller]")]
    [ApiController]
    public class ApiKeyManagementController : ControllerBase
    {
        private readonly Net9QueryStockServerContext _context;
        private readonly IApiKeyPoolService _apiKeyPoolService;
        private readonly ILogger<ApiKeyManagementController> _logger;

        public ApiKeyManagementController(
            Net9QueryStockServerContext context,
            IApiKeyPoolService apiKeyPoolService,
            ILogger<ApiKeyManagementController> logger)
        {
            _context = context;
            _apiKeyPoolService = apiKeyPoolService;
            _logger = logger;
        }

        /// <summary>
        /// 獲取所有服務提供者
        /// </summary>
        [HttpGet("providers")]
        public async Task<ActionResult<IEnumerable<ApiKeyProvider>>> GetProviders()
        {
            try
            {
                var providers = await _context.ApiKeyProviders
                    .Include(p => p.ApiKeys)
                    .OrderBy(p => p.Priority)
                    .ThenBy(p => p.Name)
                    .ToListAsync();

                return Ok(providers);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "獲取服務提供者列表時發生錯誤");
                return StatusCode(500, "獲取服務提供者列表時發生內部錯誤");
            }
        }

        /// <summary>
        /// 獲取指定提供者的統計資訊
        /// </summary>
        [HttpGet("providers/{providerName}/stats")]
        public async Task<ActionResult<ApiKeyPoolStats>> GetProviderStats(string providerName)
        {
            try
            {
                var stats = await _apiKeyPoolService.GetProviderStatsAsync(providerName);
                if (stats == null)
                {
                    return NotFound($"找不到服務提供者: {providerName}");
                }

                return Ok(stats);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "獲取提供者統計資訊時發生錯誤: {ProviderName}", providerName);
                return StatusCode(500, "獲取統計資訊時發生內部錯誤");
            }
        }

        /// <summary>
        /// 獲取所有提供者的統計資訊
        /// </summary>
        [HttpGet("stats")]
        public async Task<ActionResult<IEnumerable<ApiKeyPoolStats>>> GetAllStats()
        {
            try
            {
                var stats = await _apiKeyPoolService.GetAllProvidersStatsAsync();
                return Ok(stats);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "獲取所有統計資訊時發生錯誤");
                return StatusCode(500, "獲取統計資訊時發生內部錯誤");
            }
        }

        /// <summary>
        /// 獲取指定提供者的所有金鑰
        /// </summary>
        [HttpGet("providers/{providerName}/keys")]
        public async Task<ActionResult<IEnumerable<object>>> GetProviderKeys(string providerName)
        {
            try
            {
                var provider = await _context.ApiKeyProviders
                    .Include(p => p.ApiKeys)
                    .FirstOrDefaultAsync(p => p.Name == providerName);

                if (provider == null)
                {
                    return NotFound($"找不到服務提供者: {providerName}");
                }

                // 不返回加密的金鑰值，只返回基本資訊
                var keys = provider.ApiKeys.Select(k => new
                {
                    k.Id,
                    k.Name,
                    k.Status,
                    k.Priority,
                    k.RateLimitPerMinute,
                    k.RateLimitPerDay,
                    k.ExpiresAt,
                    k.LastUsedAt,
                    k.TotalUsageCount,
                    k.TodayUsageCount,
                    k.ConsecutiveErrorCount,
                    k.LastErrorAt,
                    k.LastErrorMessage,
                    k.SuspendedUntil,
                    k.CreatedAt,
                    k.UpdatedAt,
                    IsAvailable = k.IsAvailable
                }).ToList();

                return Ok(keys);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "獲取提供者金鑰列表時發生錯誤: {ProviderName}", providerName);
                return StatusCode(500, "獲取金鑰列表時發生內部錯誤");
            }
        }

        /// <summary>
        /// 新增 API 金鑰
        /// </summary>
        [HttpPost("providers/{providerName}/keys")]
        public async Task<ActionResult> AddApiKey(string providerName, [FromBody] AddApiKeyRequest request)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(request.KeyName) || string.IsNullOrWhiteSpace(request.ApiKey))
                {
                    return BadRequest("金鑰名稱和金鑰值不能為空");
                }

                var success = await _apiKeyPoolService.AddApiKeyAsync(
                    providerName,
                    request.KeyName,
                    request.ApiKey,
                    request.Priority ?? 1,
                    request.RateLimitPerMinute,
                    request.RateLimitPerDay,
                    request.ExpiresAt);

                if (success)
                {
                    return Ok(new { message = "API 金鑰新增成功" });
                }
                else
                {
                    return BadRequest("新增 API 金鑰失敗");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "新增 API 金鑰時發生錯誤");
                return StatusCode(500, "新增 API 金鑰時發生內部錯誤");
            }
        }

        /// <summary>
        /// 更新 API 金鑰狀態
        /// </summary>
        [HttpPut("keys/{keyId}/status")]
        public async Task<ActionResult> UpdateKeyStatus(Guid keyId, [FromBody] UpdateKeyStatusRequest request)
        {
            try
            {
                var success = await _apiKeyPoolService.UpdateKeyStatusAsync(keyId, request.Status);
                if (success)
                {
                    return Ok(new { message = "金鑰狀態更新成功" });
                }
                else
                {
                    return NotFound("找不到指定的金鑰");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新金鑰狀態時發生錯誤: {KeyId}", keyId);
                return StatusCode(500, "更新金鑰狀態時發生內部錯誤");
            }
        }

        /// <summary>
        /// 重置金鑰錯誤狀態
        /// </summary>
        [HttpPost("providers/{providerName}/keys/{keyId}/reset-errors")]
        public async Task<ActionResult> ResetKeyErrors(string providerName, Guid keyId)
        {
            try
            {
                var key = await _context.ApiKeys
                    .FirstOrDefaultAsync(k => k.Id == keyId && k.Provider.Name == providerName);

                if (key == null)
                {
                    return NotFound("找不到指定的金鑰");
                }

                // 這裡需要解密金鑰來調用服務，但為了安全起見，我們直接更新狀態
                var success = await _apiKeyPoolService.UpdateKeyStatusAsync(keyId, ApiKeyStatus.Active);
                if (success)
                {
                    return Ok(new { message = "金鑰錯誤狀態已重置" });
                }
                else
                {
                    return BadRequest("重置金鑰錯誤狀態失敗");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "重置金鑰錯誤狀態時發生錯誤: {KeyId}", keyId);
                return StatusCode(500, "重置錯誤狀態時發生內部錯誤");
            }
        }

        /// <summary>
        /// 刪除 API 金鑰
        /// </summary>
        [HttpDelete("keys/{keyId}")]
        public async Task<ActionResult> DeleteApiKey(Guid keyId)
        {
            try
            {
                var success = await _apiKeyPoolService.DeleteApiKeyAsync(keyId);
                if (success)
                {
                    return Ok(new { message = "API 金鑰刪除成功" });
                }
                else
                {
                    return NotFound("找不到指定的金鑰");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "刪除 API 金鑰時發生錯誤: {KeyId}", keyId);
                return StatusCode(500, "刪除 API 金鑰時發生內部錯誤");
            }
        }

        /// <summary>
        /// 獲取金鑰使用記錄
        /// </summary>
        [HttpGet("keys/{keyId}/usage")]
        public async Task<ActionResult<IEnumerable<ApiKeyUsage>>> GetKeyUsage(
            Guid keyId, 
            [FromQuery] int page = 1, 
            [FromQuery] int pageSize = 50)
        {
            try
            {
                var usage = await _context.ApiKeyUsages
                    .Where(u => u.ApiKeyId == keyId)
                    .OrderByDescending(u => u.RequestTime)
                    .Skip((page - 1) * pageSize)
                    .Take(pageSize)
                    .ToListAsync();

                return Ok(usage);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "獲取金鑰使用記錄時發生錯誤: {KeyId}", keyId);
                return StatusCode(500, "獲取使用記錄時發生內部錯誤");
            }
        }
    }

    /// <summary>
    /// 新增 API 金鑰請求
    /// </summary>
    public class AddApiKeyRequest
    {
        public string KeyName { get; set; } = string.Empty;
        public string ApiKey { get; set; } = string.Empty;
        public int? Priority { get; set; }
        public int? RateLimitPerMinute { get; set; }
        public int? RateLimitPerDay { get; set; }
        public DateTimeOffset? ExpiresAt { get; set; }
    }

    /// <summary>
    /// 更新金鑰狀態請求
    /// </summary>
    public class UpdateKeyStatusRequest
    {
        public ApiKeyStatus Status { get; set; }
    }
}
