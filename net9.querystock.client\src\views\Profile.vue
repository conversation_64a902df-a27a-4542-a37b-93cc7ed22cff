<template>
  <div class="profile">
    <!-- 導航欄 -->
    <nav class="navbar">
      <div class="nav-brand">
        <h1>股票查詢平台</h1>
      </div>
      <div class="nav-menu">
        <router-link to="/" class="nav-link">首頁</router-link>
        <router-link to="/dashboard" class="nav-link">儀表板</router-link>
        <router-link to="/keys" class="nav-link">金鑰管理</router-link>
        <router-link to="/profile" class="nav-link">個人資料</router-link>
        <button @click="handleLogout" class="nav-link logout-btn">登出</button>
      </div>
    </nav>

    <div class="content">
      <div class="page-header">
        <h2>個人資料</h2>
        <p>管理您的帳戶資訊和安全設定</p>
      </div>

      <!-- 用戶資訊卡片 -->
      <div class="profile-card">
        <div class="profile-header">
          <div class="avatar">
            <span class="avatar-text">{{ userInitials }}</span>
          </div>
          <div class="user-info">
            <h3>{{ user?.username }}</h3>
            <p>{{ user?.email || '未設定電子郵件' }}</p>
            <p class="user-role">{{ getRoleText(user?.role) }}</p>
          </div>
        </div>
        
        <div class="profile-stats">
          <div class="stat-item">
            <span class="stat-label">註冊時間</span>
            <span class="stat-value">{{ formatDate(user?.createdAt) }}</span>
          </div>
          <div class="stat-item">
            <span class="stat-label">最後登入</span>
            <span class="stat-value">{{ formatDate(user?.lastLoginAt) }}</span>
          </div>
          <div class="stat-item">
            <span class="stat-label">帳戶狀態</span>
            <span class="stat-value" :class="user?.isActive ? 'active' : 'inactive'">
              {{ user?.isActive ? '啟用' : '停用' }}
            </span>
          </div>
        </div>
      </div>

      <!-- 修改密碼 -->
      <div class="password-section">
        <h3>修改密碼</h3>
        <form @submit.prevent="changePassword" class="password-form">
          <div class="form-group">
            <label for="currentPassword">當前密碼</label>
            <input
              id="currentPassword"
              v-model="passwordForm.currentPassword"
              type="password"
              placeholder="請輸入當前密碼"
              required
              :disabled="submitting"
            />
          </div>
          
          <div class="form-group">
            <label for="newPassword">新密碼</label>
            <input
              id="newPassword"
              v-model="passwordForm.newPassword"
              type="password"
              placeholder="請輸入新密碼（至少6字元）"
              required
              minlength="6"
              :disabled="submitting"
            />
          </div>
          
          <div class="form-group">
            <label for="confirmNewPassword">確認新密碼</label>
            <input
              id="confirmNewPassword"
              v-model="passwordForm.confirmNewPassword"
              type="password"
              placeholder="請再次輸入新密碼"
              required
              :disabled="submitting"
            />
          </div>
          
          <div v-if="passwordError" class="error-message">
            {{ passwordError }}
          </div>
          
          <div v-if="passwordSuccess" class="success-message">
            {{ passwordSuccess }}
          </div>
          
          <div v-if="passwordMismatch" class="error-message">
            新密碼和確認密碼不一致
          </div>
          
          <button type="submit" class="btn btn-primary" :disabled="submitting || passwordMismatch">
            {{ submitting ? '修改中...' : '修改密碼' }}
          </button>
        </form>
      </div>

      <!-- 帳戶設定 -->
      <div class="settings-section">
        <h3>帳戶設定</h3>
        <div class="settings-grid">
          <div class="setting-item">
            <div class="setting-info">
              <h4>電子郵件通知</h4>
              <p>接收重要的帳戶和系統通知</p>
            </div>
            <label class="toggle">
              <input type="checkbox" v-model="settings.emailNotifications" @change="updateSettings">
              <span class="toggle-slider"></span>
            </label>
          </div>
          
          <div class="setting-item">
            <div class="setting-info">
              <h4>API 使用統計</h4>
              <p>在儀表板顯示詳細的 API 使用統計</p>
            </div>
            <label class="toggle">
              <input type="checkbox" v-model="settings.showDetailedStats" @change="updateSettings">
              <span class="toggle-slider"></span>
            </label>
          </div>
          
          <div class="setting-item">
            <div class="setting-info">
              <h4>自動刷新</h4>
              <p>自動刷新儀表板和統計資料</p>
            </div>
            <label class="toggle">
              <input type="checkbox" v-model="settings.autoRefresh" @change="updateSettings">
              <span class="toggle-slider"></span>
            </label>
          </div>
        </div>
      </div>

      <!-- 危險區域 -->
      <div class="danger-section">
        <h3>危險區域</h3>
        <div class="danger-content">
          <div class="danger-info">
            <h4>刪除帳戶</h4>
            <p>永久刪除您的帳戶和所有相關資料。此操作無法復原。</p>
          </div>
          <button @click="confirmDeleteAccount" class="btn btn-danger">
            刪除帳戶
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '../stores/auth'

export default {
  name: 'Profile',
  setup() {
    const router = useRouter()
    const authStore = useAuthStore()
    
    const submitting = ref(false)
    const passwordError = ref('')
    const passwordSuccess = ref('')
    
    const passwordForm = ref({
      currentPassword: '',
      newPassword: '',
      confirmNewPassword: ''
    })
    
    const settings = ref({
      emailNotifications: true,
      showDetailedStats: true,
      autoRefresh: false
    })
    
    const user = computed(() => authStore.user)
    
    const userInitials = computed(() => {
      if (!user.value?.username) return 'U'
      return user.value.username.substring(0, 2).toUpperCase()
    })
    
    const passwordMismatch = computed(() => {
      return passwordForm.value.newPassword && passwordForm.value.confirmNewPassword && 
             passwordForm.value.newPassword !== passwordForm.value.confirmNewPassword
    })
    
    const handleLogout = () => {
      authStore.logout()
      router.push('/')
    }
    
    const changePassword = async () => {
      if (passwordMismatch.value) {
        return
      }
      
      submitting.value = true
      passwordError.value = ''
      passwordSuccess.value = ''
      
      try {
        const result = await authStore.changePassword(passwordForm.value)
        
        if (result.success) {
          passwordSuccess.value = result.message
          // 清空表單
          passwordForm.value = {
            currentPassword: '',
            newPassword: '',
            confirmNewPassword: ''
          }
        } else {
          passwordError.value = result.message
        }
      } catch (error) {
        passwordError.value = '修改密碼時發生錯誤，請稍後再試'
      } finally {
        submitting.value = false
      }
    }
    
    const updateSettings = () => {
      // 這裡可以將設定保存到後端或本地存儲
      localStorage.setItem('userSettings', JSON.stringify(settings.value))
    }
    
    const loadSettings = () => {
      const saved = localStorage.getItem('userSettings')
      if (saved) {
        try {
          settings.value = { ...settings.value, ...JSON.parse(saved) }
        } catch (error) {
          console.error('載入設定失敗:', error)
        }
      }
    }
    
    const confirmDeleteAccount = () => {
      if (confirm('確定要刪除您的帳戶嗎？此操作無法復原，所有資料將永久遺失。')) {
        if (confirm('請再次確認：您真的要刪除帳戶嗎？')) {
          deleteAccount()
        }
      }
    }
    
    const deleteAccount = async () => {
      // TODO: 實作刪除帳戶功能
      alert('刪除帳戶功能開發中...')
    }
    
    const getRoleText = (role) => {
      const roleMap = {
        'User': '一般用戶',
        'Admin': '管理員',
        'Premium': '高級用戶'
      }
      return roleMap[role] || role
    }
    
    const formatDate = (dateString) => {
      if (!dateString) return '未知'
      return new Date(dateString).toLocaleString('zh-TW')
    }
    
    onMounted(() => {
      loadSettings()
    })
    
    return {
      user,
      userInitials,
      submitting,
      passwordError,
      passwordSuccess,
      passwordForm,
      passwordMismatch,
      settings,
      handleLogout,
      changePassword,
      updateSettings,
      confirmDeleteAccount,
      getRoleText,
      formatDate
    }
  }
}
</script>

<style scoped>
.profile {
  min-height: 100vh;
  background-color: #f8f9fa;
}

.navbar {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 1rem 2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.nav-brand h1 {
  margin: 0;
  font-size: 1.5rem;
  font-weight: 600;
}

.nav-menu {
  display: flex;
  gap: 1rem;
  align-items: center;
}

.nav-link {
  color: white;
  text-decoration: none;
  padding: 0.5rem 1rem;
  border-radius: 6px;
  transition: background-color 0.3s ease;
  font-weight: 500;
}

.nav-link:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.nav-link.router-link-active {
  background-color: rgba(255, 255, 255, 0.2);
}

.logout-btn {
  background: none;
  border: none;
  cursor: pointer;
  font-size: inherit;
  font-family: inherit;
}

.content {
  max-width: 800px;
  margin: 0 auto;
  padding: 2rem;
}

.page-header {
  background: white;
  padding: 2rem;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  margin-bottom: 2rem;
}

.page-header h2 {
  margin: 0 0 1rem 0;
  color: #333;
  font-size: 2rem;
}

.page-header p {
  margin: 0;
  color: #666;
  font-size: 1.1rem;
}

.profile-card,
.password-section,
.settings-section,
.danger-section {
  background: white;
  padding: 2rem;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  margin-bottom: 2rem;
}

.profile-header {
  display: flex;
  align-items: center;
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.avatar {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 2rem;
  font-weight: 600;
}

.user-info h3 {
  margin: 0 0 0.5rem 0;
  color: #333;
  font-size: 1.5rem;
}

.user-info p {
  margin: 0 0 0.25rem 0;
  color: #666;
}

.user-role {
  color: #667eea !important;
  font-weight: 600;
}

.profile-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
}

.stat-item {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.stat-label {
  font-weight: 500;
  color: #666;
  font-size: 0.9rem;
}

.stat-value {
  color: #333;
  font-weight: 600;
}

.stat-value.active {
  color: #28a745;
}

.stat-value.inactive {
  color: #dc3545;
}

.password-section h3,
.settings-section h3,
.danger-section h3 {
  margin: 0 0 1.5rem 0;
  color: #333;
  font-size: 1.5rem;
}

.password-form {
  max-width: 400px;
}

.form-group {
  margin-bottom: 1rem;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: #333;
}

.form-group input {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 1rem;
  box-sizing: border-box;
}

.form-group input:focus {
  outline: none;
  border-color: #667eea;
}

.error-message {
  background-color: #fee;
  color: #c33;
  padding: 0.75rem;
  border-radius: 6px;
  margin-bottom: 1rem;
  border: 1px solid #fcc;
  font-size: 0.9rem;
}

.success-message {
  background-color: #efe;
  color: #3c3;
  padding: 0.75rem;
  border-radius: 6px;
  margin-bottom: 1rem;
  border: 1px solid #cfc;
  font-size: 0.9rem;
}

.btn {
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.btn-primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.btn-danger {
  background: #dc3545;
  color: white;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.settings-grid {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.setting-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  border: 1px solid #e9ecef;
  border-radius: 8px;
}

.setting-info h4 {
  margin: 0 0 0.25rem 0;
  color: #333;
}

.setting-info p {
  margin: 0;
  color: #666;
  font-size: 0.9rem;
}

.toggle {
  position: relative;
  display: inline-block;
  width: 50px;
  height: 24px;
}

.toggle input {
  opacity: 0;
  width: 0;
  height: 0;
}

.toggle-slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #ccc;
  transition: 0.4s;
  border-radius: 24px;
}

.toggle-slider:before {
  position: absolute;
  content: "";
  height: 18px;
  width: 18px;
  left: 3px;
  bottom: 3px;
  background-color: white;
  transition: 0.4s;
  border-radius: 50%;
}

.toggle input:checked + .toggle-slider {
  background-color: #667eea;
}

.toggle input:checked + .toggle-slider:before {
  transform: translateX(26px);
}

.danger-section {
  border: 1px solid #dc3545;
}

.danger-section h3 {
  color: #dc3545;
}

.danger-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.danger-info h4 {
  margin: 0 0 0.5rem 0;
  color: #dc3545;
}

.danger-info p {
  margin: 0;
  color: #666;
}

@media (max-width: 768px) {
  .nav-menu {
    flex-direction: column;
    gap: 0.5rem;
  }
  
  .content {
    padding: 1rem;
  }
  
  .profile-header {
    flex-direction: column;
    text-align: center;
  }
  
  .profile-stats {
    grid-template-columns: 1fr;
  }
  
  .setting-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }
  
  .danger-content {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }
}
</style>
