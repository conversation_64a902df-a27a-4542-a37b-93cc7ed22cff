﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="Current" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <ActiveDebugProfile>Container (Dockerfile)</ActiveDebugProfile>
    <_SelectedScaffolderID>ApiControllerWithContextScaffolder</_SelectedScaffolderID>
    <_SelectedScaffolderCategoryPath>root/Common</_SelectedScaffolderCategoryPath>
    <WebStackScaffolding_ControllerDialogWidth>650</WebStackScaffolding_ControllerDialogWidth>
    <WebStackScaffolding_DbContextDialogWidth>650</WebStackScaffolding_DbContextDialogWidth>
    <WebStackScaffolding_IsLayoutPageSelected>True</WebStackScaffolding_IsLayoutPageSelected>
    <WebStackScaffolding_IsPartialViewSelected>False</WebStackScaffolding_IsPartialViewSelected>
    <WebStackScaffolding_IsReferencingScriptLibrariesSelected>True</WebStackScaffolding_IsReferencingScriptLibrariesSelected>
    <WebStackScaffolding_LayoutPageFile />
    <WebStackScaffolding_DbContextTypeFullName>Net9.QueryStock.Server.Data.Net9QueryStockServerContext</WebStackScaffolding_DbContextTypeFullName>
    <WebStackScaffolding_IsAsyncSelected>False</WebStackScaffolding_IsAsyncSelected>
  </PropertyGroup>
</Project>