<template>
  <div class="home">
    <!-- 歡迎區域 -->
    <section class="hero">
      <div class="hero-content">
        <h1>歡迎使用股票查詢平台</h1>
        <p>即時獲取準確的股票資訊，支援多種股票市場查詢</p>
        <div class="hero-actions">
          <router-link to="/setup" class="btn btn-primary">開始設定</router-link>
          <router-link to="/register" class="btn btn-outline" v-if="!isLoggedIn">免費註冊</router-link>
        </div>
      </div>
    </section>

    <!-- 股票查詢區域 -->
    <section class="search-section">
      <div class="section-header">
        <h2>股票查詢</h2>
        <p>輸入股票代碼或公司名稱來查詢即時股價資訊</p>
      </div>
      <StockSearchForm />
    </section>

    <!-- 功能特色 -->
    <section class="features">
      <div class="section-header">
        <h2>平台特色</h2>
        <p>為什麼選擇我們的股票查詢平台</p>
      </div>
      <div class="features-grid">
        <div class="feature-card">
          <div class="feature-icon">⚡</div>
          <h3>即時資料</h3>
          <p>提供即時股票價格、成交量等關鍵資訊</p>
        </div>
        <div class="feature-card">
          <div class="feature-icon">🔒</div>
          <h3>安全可靠</h3>
          <p>採用企業級安全措施保護您的資料</p>
        </div>
        <div class="feature-card">
          <div class="feature-icon">📊</div>
          <h3>多元分析</h3>
          <p>提供詳細的股票分析和歷史價格圖表</p>
        </div>
        <div class="feature-card">
          <div class="feature-icon">🌐</div>
          <h3>全球市場</h3>
          <p>支援美股、港股等多個國際股票市場</p>
        </div>
      </div>
    </section>
  </div>
</template>

<script>
import { computed } from 'vue'
import { useAuthStore } from '../stores/auth'
import StockSearchForm from '../components/StockSearchForm.vue'

export default {
  name: 'Home',
  components: {
    StockSearchForm
  },
  setup() {
    const authStore = useAuthStore()
    const isLoggedIn = computed(() => authStore.isLoggedIn)

    return {
      isLoggedIn
    }
  }
}
</script>

<style scoped>
.home {
  min-height: 100vh;
}

/* 英雄區域 */
.hero {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 4rem 0;
  text-align: center;
  margin: -2rem -1rem 0;
}

.hero-content h1 {
  margin: 0 0 1rem 0;
  font-size: 3rem;
  font-weight: 700;
}

.hero-content p {
  margin: 0 0 2rem 0;
  font-size: 1.2rem;
  opacity: 0.9;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

.hero-actions {
  display: flex;
  gap: 1rem;
  justify-content: center;
  flex-wrap: wrap;
}

.btn {
  padding: 1rem 2rem;
  border-radius: 8px;
  text-decoration: none;
  font-weight: 600;
  transition: all 0.3s ease;
  border: 2px solid transparent;
  cursor: pointer;
  display: inline-block;
}

.btn-primary {
  background: white;
  color: #667eea;
}

.btn-primary:hover {
  background: #f8f9fa;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.btn-outline {
  background: transparent;
  color: white;
  border-color: rgba(255, 255, 255, 0.5);
}

.btn-outline:hover {
  background: rgba(255, 255, 255, 0.1);
  border-color: white;
  transform: translateY(-2px);
}

/* 區段樣式 */
.search-section,
.features {
  padding: 4rem 0;
}

.section-header {
  text-align: center;
  margin-bottom: 3rem;
}

.section-header h2 {
  margin: 0 0 1rem 0;
  font-size: 2.5rem;
  color: #333;
  font-weight: 600;
}

.section-header p {
  margin: 0;
  font-size: 1.1rem;
  color: #666;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

/* 功能特色 */
.features {
  background: white;
  margin: 0 -1rem;
  padding: 4rem 1rem;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
  max-width: 1000px;
  margin: 0 auto;
}

.feature-card {
  text-align: center;
  padding: 2rem;
  border-radius: 12px;
  background: #f8f9fa;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.feature-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.feature-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
}

.feature-card h3 {
  margin: 0 0 1rem 0;
  color: #333;
  font-size: 1.3rem;
  font-weight: 600;
}

.feature-card p {
  margin: 0;
  color: #666;
  line-height: 1.6;
}

/* 響應式設計 */
@media (max-width: 768px) {
  .hero {
    padding: 3rem 1rem;
  }

  .hero-content h1 {
    font-size: 2.5rem;
  }

  .hero-content p {
    font-size: 1.1rem;
  }

  .hero-actions {
    flex-direction: column;
    align-items: center;
  }

  .btn {
    width: 100%;
    max-width: 300px;
  }

  .section-header h2 {
    font-size: 2rem;
  }

  .features-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .search-section,
  .features {
    padding: 3rem 0;
  }
}

@media (max-width: 480px) {
  .hero-content h1 {
    font-size: 2rem;
  }

  .feature-card {
    padding: 1.5rem;
  }
}
</style>
