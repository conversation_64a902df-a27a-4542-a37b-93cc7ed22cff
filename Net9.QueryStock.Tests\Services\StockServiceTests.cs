using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Moq;
using Net9.QueryStock.Server.Data;
using Net9.QueryStock.Server.DTOs;
using Net9.QueryStock.Server.Interfaces;
using Net9.QueryStock.Server.Models;
using Net9.QueryStock.Server.Services;

namespace Net9.QueryStock.Tests.Services
{
    /// <summary>
    /// StockService 單元測試
    /// </summary>
    public class StockServiceTests
    {
        private readonly Mock<IStockDataProvider> _mockDataProvider;
        private readonly Mock<ICacheService> _mockCacheService;
        private readonly Mock<ILogger<StockService>> _mockLogger;
        private readonly Net9QueryStockServerContext _dbContext;

        public StockServiceTests()
        {
            _mockDataProvider = new Mock<IStockDataProvider>();
            _mockCacheService = new Mock<ICacheService>();
            _mockLogger = new Mock<ILogger<StockService>>();
            
            // 設定資料提供者
            _mockDataProvider.Setup(p => p.IsAvailable).Returns(true);
            _mockDataProvider.Setup(p => p.ProviderName).Returns("TestProvider");
            
            // 設定記憶體資料庫
            var options = new DbContextOptionsBuilder<Net9QueryStockServerContext>()
                .UseInMemoryDatabase(databaseName: Guid.NewGuid().ToString())
                .Options;
                
            _dbContext = new Net9QueryStockServerContext(options);
        }

        [Fact]
        public async Task GetRealTimeQuoteAsync_WhenCacheHit_ShouldReturnCachedQuote()
        {
            // Arrange
            var symbol = "AAPL";
            var cachedQuote = new StockQuoteDto
            {
                Symbol = symbol,
                CurrentPrice = 150.25m,
                DataSource = "Cache"
            };
            
            _mockCacheService
                .Setup(c => c.GetAsync<StockQuoteDto>(It.IsAny<string>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(cachedQuote);
                
            var service = CreateService();

            // Act
            var result = await service.GetRealTimeQuoteAsync(symbol);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(cachedQuote, result);
            _mockDataProvider.Verify(p => p.GetRealTimeQuoteAsync(It.IsAny<string>(), It.IsAny<CancellationToken>()), Times.Never);
        }

        [Fact]
        public async Task GetRealTimeQuoteAsync_WhenCacheMiss_ShouldCallProvider()
        {
            // Arrange
            var symbol = "AAPL";
            var providerQuote = new StockQuoteDto
            {
                Symbol = symbol,
                CurrentPrice = 150.25m,
                DataSource = "TestProvider"
            };
            
            _mockCacheService
                .Setup(c => c.GetAsync<StockQuoteDto>(It.IsAny<string>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync((StockQuoteDto?)null);
                
            _mockDataProvider
                .Setup(p => p.GetRealTimeQuoteAsync(symbol, It.IsAny<CancellationToken>()))
                .ReturnsAsync(providerQuote);
                
            var service = CreateService();

            // Act
            var result = await service.GetRealTimeQuoteAsync(symbol);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(providerQuote, result);
            _mockDataProvider.Verify(p => p.GetRealTimeQuoteAsync(symbol, It.IsAny<CancellationToken>()), Times.Once);
            _mockCacheService.Verify(c => c.SetAsync(It.IsAny<string>(), providerQuote, It.IsAny<TimeSpan?>(), It.IsAny<CancellationToken>()), Times.Once);
        }

        [Fact]
        public async Task GetStockInfoAsync_WhenCacheHit_ShouldReturnCachedInfo()
        {
            // Arrange
            var symbol = "AAPL";
            var cachedInfo = new StockInfoDto
            {
                Symbol = symbol,
                Name = "Apple Inc"
            };
            
            _mockCacheService
                .Setup(c => c.GetAsync<StockInfoDto>(It.IsAny<string>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(cachedInfo);
                
            var service = CreateService();

            // Act
            var result = await service.GetStockInfoAsync(symbol);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(cachedInfo, result);
            _mockDataProvider.Verify(p => p.GetStockInfoAsync(It.IsAny<string>(), It.IsAny<CancellationToken>()), Times.Never);
        }

        [Fact]
        public async Task SyncStockDataAsync_WhenSuccessful_ShouldSaveToDatabase()
        {
            // Arrange
            var symbol = "AAPL";
            var stockInfo = new StockInfoDto
            {
                Symbol = symbol,
                Name = "Apple Inc",
                Exchange = "NASDAQ",
                Currency = "USD",
                Industry = "Technology"
            };
            
            var stockQuote = new StockQuoteDto
            {
                Symbol = symbol,
                CurrentPrice = 150.25m,
                OpenPrice = 149.50m,
                HighPrice = 151.00m,
                LowPrice = 149.00m,
                Volume = 1000000,
                DataSource = "TestProvider"
            };
            
            _mockDataProvider
                .Setup(p => p.GetStockInfoAsync(symbol, It.IsAny<CancellationToken>()))
                .ReturnsAsync(stockInfo);
                
            _mockDataProvider
                .Setup(p => p.GetRealTimeQuoteAsync(symbol, It.IsAny<CancellationToken>()))
                .ReturnsAsync(stockQuote);
                
            var service = CreateService();

            // Act
            var result = await service.SyncStockDataAsync(symbol);

            // Assert
            Assert.True(result);
            
            // 驗證資料庫中是否有資料
            var savedStock = await _dbContext.Stock.FirstOrDefaultAsync(s => s.Symbol == symbol);
            Assert.NotNull(savedStock);
            Assert.Equal(stockInfo.Name, savedStock.Name);
            Assert.Equal(stockInfo.Exchange, savedStock.Exchange);
        }

        [Fact]
        public async Task GetStoredStocksAsync_ShouldReturnActiveStocks()
        {
            // Arrange
            var stocks = new List<Stock>
            {
                new Stock { Id = Guid.NewGuid(), Symbol = "AAPL", Name = "Apple Inc", IsActive = true },
                new Stock { Id = Guid.NewGuid(), Symbol = "MSFT", Name = "Microsoft Corp", IsActive = true },
                new Stock { Id = Guid.NewGuid(), Symbol = "GOOG", Name = "Alphabet Inc", IsActive = false }
            };
            
            await _dbContext.Stock.AddRangeAsync(stocks);
            await _dbContext.SaveChangesAsync();
            
            var service = CreateService();

            // Act
            var result = await service.GetStoredStocksAsync();

            // Assert
            var resultList = result.ToList();
            Assert.Equal(2, resultList.Count);
            Assert.Contains(resultList, s => s.Symbol == "AAPL");
            Assert.Contains(resultList, s => s.Symbol == "MSFT");
            Assert.DoesNotContain(resultList, s => s.Symbol == "GOOG");
        }

        private StockService CreateService()
        {
            return new StockService(
                _dbContext,
                new[] { _mockDataProvider.Object },
                _mockCacheService.Object,
                _mockLogger.Object);
        }
    }
}
