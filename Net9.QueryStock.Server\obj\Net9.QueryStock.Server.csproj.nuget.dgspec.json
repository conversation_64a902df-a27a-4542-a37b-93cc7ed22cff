{"format": 1, "restore": {"C:\\Users\\<USER>\\source\\repos\\Net9.QueryStock\\Net9.QueryStock.Server\\Net9.QueryStock.Server.csproj": {}}, "projects": {"C:\\Users\\<USER>\\source\\repos\\Net9.QueryStock\\Net9.QueryStock.Server\\Net9.QueryStock.Server.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\source\\repos\\Net9.QueryStock\\Net9.QueryStock.Server\\Net9.QueryStock.Server.csproj", "projectName": "Net9.QueryStock.Server", "projectPath": "C:\\Users\\<USER>\\source\\repos\\Net9.QueryStock\\Net9.QueryStock.Server\\Net9.QueryStock.Server.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\source\\repos\\Net9.QueryStock\\Net9.QueryStock.Server\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"Microsoft.AspNetCore.Authentication.JwtBearer": {"target": "Package", "version": "[9.0.7, )"}, "Microsoft.AspNetCore.Mvc.Testing": {"target": "Package", "version": "[9.0.7, )"}, "Microsoft.AspNetCore.OpenApi": {"target": "Package", "version": "[9.0.7, )"}, "Microsoft.AspNetCore.SpaProxy": {"target": "Package", "version": "[9.*-*, )"}, "Microsoft.EntityFrameworkCore": {"target": "Package", "version": "[9.0.7, )"}, "Microsoft.EntityFrameworkCore.InMemory": {"target": "Package", "version": "[9.0.7, )"}, "Microsoft.EntityFrameworkCore.SqlServer": {"target": "Package", "version": "[9.0.7, )"}, "Microsoft.EntityFrameworkCore.Tools": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[9.0.7, )"}, "Microsoft.Extensions.Http": {"target": "Package", "version": "[9.0.7, )"}, "Microsoft.Extensions.Logging.Abstractions": {"target": "Package", "version": "[9.0.7, )"}, "Microsoft.VisualStudio.Azure.Containers.Tools.Targets": {"target": "Package", "version": "[1.22.1, )"}, "Microsoft.VisualStudio.Web.CodeGeneration.Design": {"target": "Package", "version": "[9.0.0, )"}, "Moq": {"target": "Package", "version": "[4.20.72, )"}, "xunit": {"target": "Package", "version": "[2.9.2, )"}, "xunit.runner.visualstudio": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[2.8.2, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301/PortableRuntimeIdentifierGraph.json"}}}}}