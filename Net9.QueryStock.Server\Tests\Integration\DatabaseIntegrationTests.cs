using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Net9.QueryStock.Server.Data;
using Net9.QueryStock.Server.Models;
using Net9.QueryStock.Server.Services;
using System.Linq;
using Xunit;

namespace Net9.QueryStock.Server.Tests.Integration
{
    /// <summary>
    /// 資料庫整合測試
    /// 測試實際的資料庫操作、關聯性和約束
    /// </summary>
    public class DatabaseIntegrationTests : TestBase
    {
        [Fact]
        public async Task DatabaseSchema_CreatedCorrectly()
        {
            // Act & Assert
            // 驗證所有表格都存在
            Assert.True(await Context.Database.CanConnectAsync());
            
            // 驗證主要表格存在
            var tableNames = new[]
            {
                "ApiKeyProviders",
                "ApiKeys", 
                "ApiKeyUsages",
                "Users"
            };

            foreach (var tableName in tableNames)
            {
                var tableExists = await Context.Database.ExecuteSqlRawAsync(
                    $"SELECT COUNT(*) FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = '{tableName}'") >= 0;
                Assert.True(tableExists, $"表格 {tableName} 應該存在");
            }
        }

        [Fact]
        public async Task ApiKeyProvider_CascadeDelete_WorksCorrectly()
        {
            // Arrange
            await CleanupTestDataAsync();
            await SeedTestApiKeyProviderAsync();
            
            var provider = await Context.ApiKeyProviders.FirstAsync(p => p.Name == "Finnhub");
            var testKey = await CreateTestApiKeyAsync("Finnhub", "CascadeTestKey", "test_key");

            // 建立使用記錄
            var usage = new ApiKeyUsage
            {
                Id = Guid.NewGuid(),
                ApiKeyId = testKey.Id,
                Endpoint = "/test",
                IsSuccess = true,
                ResponseTimeMs = 100,
                RequestTime = DateTimeOffset.UtcNow,
                HttpStatusCode = 200
            };
            Context.ApiKeyUsages.Add(usage);
            await Context.SaveChangesAsync();

            // Act - 刪除提供者
            Context.ApiKeyProviders.Remove(provider);
            await Context.SaveChangesAsync();

            // Assert - 相關的金鑰和使用記錄應該被級聯刪除
            var remainingKeys = await Context.ApiKeys.Where(k => k.ProviderId == provider.Id).ToListAsync();
            var remainingUsages = await Context.ApiKeyUsages.Where(u => u.ApiKeyId == testKey.Id).ToListAsync();
            
            Assert.Empty(remainingKeys);
            Assert.Empty(remainingUsages);
        }

        [Fact]
        public async Task ApiKey_UniqueConstraints_EnforcedCorrectly()
        {
            // Arrange
            await CleanupTestDataAsync();
            await SeedTestApiKeyProviderAsync();
            
            var provider = await Context.ApiKeyProviders.FirstAsync(p => p.Name == "Finnhub");
            
            var firstKey = new ApiKey
            {
                Id = Guid.NewGuid(),
                Name = "DuplicateNameTest",
                EncryptedKey = "encrypted_key_1",
                ProviderId = provider.Id,
                Priority = 1,
                RateLimitPerMinute = 60,
                RateLimitPerDay = 1000,
                Status = ApiKeyStatus.Active,
                CreatedAt = DateTimeOffset.UtcNow,
                UpdatedAt = DateTimeOffset.UtcNow
            };

            Context.ApiKeys.Add(firstKey);
            await Context.SaveChangesAsync();

            // Act & Assert - 嘗試添加相同名稱的金鑰應該失敗
            var duplicateKey = new ApiKey
            {
                Id = Guid.NewGuid(),
                Name = "DuplicateNameTest", // 相同名稱
                EncryptedKey = "encrypted_key_2",
                ProviderId = provider.Id,
                Priority = 2,
                RateLimitPerMinute = 60,
                RateLimitPerDay = 1000,
                Status = ApiKeyStatus.Active,
                CreatedAt = DateTimeOffset.UtcNow,
                UpdatedAt = DateTimeOffset.UtcNow
            };

            Context.ApiKeys.Add(duplicateKey);
            
            await Assert.ThrowsAsync<DbUpdateException>(async () => await Context.SaveChangesAsync());
        }

        [Fact]
        public async Task User_UniqueConstraints_EnforcedCorrectly()
        {
            // Arrange
            await CleanupTestDataAsync();
            await CreateTestUserAsync("uniqueuser", "password123", "<EMAIL>");

            // Act & Assert - 嘗試添加相同用戶名應該失敗
            var duplicateUsernameUser = new User
            {
                Id = Guid.NewGuid(),
                Username = "uniqueuser", // 相同用戶名
                Email = "<EMAIL>",
                PasswordHash = "hash",
                Salt = "salt",
                Role = "User",
                IsActive = true,
                CreatedAt = DateTimeOffset.UtcNow,
                UpdatedAt = DateTimeOffset.UtcNow
            };

            Context.Users.Add(duplicateUsernameUser);
            await Assert.ThrowsAsync<DbUpdateException>(async () => await Context.SaveChangesAsync());

            // 清理
            Context.Users.Remove(duplicateUsernameUser);

            // 嘗試添加相同電子郵件應該失敗
            var duplicateEmailUser = new User
            {
                Id = Guid.NewGuid(),
                Username = "differentuser",
                Email = "<EMAIL>", // 相同電子郵件
                PasswordHash = "hash",
                Salt = "salt",
                Role = "User",
                IsActive = true,
                CreatedAt = DateTimeOffset.UtcNow,
                UpdatedAt = DateTimeOffset.UtcNow
            };

            Context.Users.Add(duplicateEmailUser);
            await Assert.ThrowsAsync<DbUpdateException>(async () => await Context.SaveChangesAsync());
        }

        [Fact]
        public async Task ApiKeyUsage_ForeignKeyConstraints_EnforcedCorrectly()
        {
            // Arrange
            await CleanupTestDataAsync();
            
            var invalidKeyId = Guid.NewGuid();
            var usage = new ApiKeyUsage
            {
                Id = Guid.NewGuid(),
                ApiKeyId = invalidKeyId, // 不存在的金鑰 ID
                Endpoint = "/test",
                IsSuccess = true,
                ResponseTimeMs = 100,
                RequestTime = DateTimeOffset.UtcNow,
                HttpStatusCode = 200
            };

            // Act & Assert
            Context.ApiKeyUsages.Add(usage);
            await Assert.ThrowsAsync<DbUpdateException>(async () => await Context.SaveChangesAsync());
        }

        [Fact]
        public async Task DatabaseSeeder_WorksCorrectly()
        {
            // Arrange
            await CleanupTestDataAsync();
            var seeder = ServiceProvider.GetRequiredService<DatabaseSeederService>();

            // Act
            await seeder.SeedAsync();

            // Assert
            var providers = await Context.ApiKeyProviders.ToListAsync();
            Assert.True(providers.Count >= 1, "應該至少有一個 API 提供者");
            
            var finnhubProvider = providers.FirstOrDefault(p => p.Name == "Finnhub");
            Assert.NotNull(finnhubProvider);
            Assert.True(finnhubProvider.IsEnabled);
            Assert.NotNull(finnhubProvider.DisplayName);
            Assert.NotNull(finnhubProvider.Description);
        }

        [Fact]
        public async Task ComplexQuery_WithJoins_WorksCorrectly()
        {
            // Arrange
            await CleanupTestDataAsync();
            await SeedTestApiKeyProviderAsync();
            var testKey = await CreateTestApiKeyAsync("Finnhub", "ComplexQueryTest", "test_key");

            // 建立多個使用記錄
            var usages = new[]
            {
                new ApiKeyUsage
                {
                    Id = Guid.NewGuid(),
                    ApiKeyId = testKey.Id,
                    Endpoint = "/quote",
                    IsSuccess = true,
                    ResponseTimeMs = 100,
                    RequestTime = DateTimeOffset.UtcNow.AddHours(-1),
                    HttpStatusCode = 200
                },
                new ApiKeyUsage
                {
                    Id = Guid.NewGuid(),
                    ApiKeyId = testKey.Id,
                    Endpoint = "/search",
                    IsSuccess = false,
                    ResponseTimeMs = 500,
                    ErrorMessage = "Rate limit exceeded",
                    RequestTime = DateTimeOffset.UtcNow.AddMinutes(-30),
                    HttpStatusCode = 429
                }
            };

            Context.ApiKeyUsages.AddRange(usages);
            await Context.SaveChangesAsync();

            // Act - 執行複雜查詢
            var result = await Context.ApiKeyProviders
                .Include(p => p.ApiKeys)
                .ThenInclude(k => k.UsageRecords)
                .Where(p => p.Name == "Finnhub")
                .Select(p => new
                {
                    ProviderName = p.Name,
                    TotalKeys = p.ApiKeys.Count,
                    TotalUsage = p.ApiKeys.SelectMany(k => k.UsageRecords).Count(),
                    SuccessfulUsage = p.ApiKeys.SelectMany(k => k.UsageRecords).Count(u => u.IsSuccess),
                    AverageResponseTime = p.ApiKeys.SelectMany(k => k.UsageRecords).Average(u => u.ResponseTimeMs)
                })
                .FirstOrDefaultAsync();

            // Assert
            Assert.NotNull(result);
            Assert.Equal("Finnhub", result.ProviderName);
            Assert.Equal(1, result.TotalKeys);
            Assert.Equal(2, result.TotalUsage);
            Assert.Equal(1, result.SuccessfulUsage);
            Assert.Equal(300, result.AverageResponseTime); // (100 + 500) / 2
        }

        [Fact]
        public async Task Transaction_RollbackOnError_WorksCorrectly()
        {
            // Arrange
            await CleanupTestDataAsync();
            await SeedTestApiKeyProviderAsync();

            // Act & Assert
            await Assert.ThrowsAsync<InvalidOperationException>(async () =>
            {
                using var transaction = await Context.Database.BeginTransactionAsync();
                try
                {
                    // 添加有效的金鑰
                    var testKey = await CreateTestApiKeyAsync("Finnhub", "TransactionTest", "test_key");
                    
                    // 故意引發錯誤
                    throw new InvalidOperationException("測試錯誤");
                    
                    await transaction.CommitAsync();
                }
                catch
                {
                    await transaction.RollbackAsync();
                    throw;
                }
            });

            // 驗證回滾成功 - 金鑰不應該存在
            var keys = await Context.ApiKeys.Where(k => k.Name == "TransactionTest").ToListAsync();
            Assert.Empty(keys);
        }

        [Fact]
        public async Task ConcurrentAccess_HandledCorrectly()
        {
            // Arrange
            await CleanupTestDataAsync();
            await SeedTestApiKeyProviderAsync();
            var testKey = await CreateTestApiKeyAsync("Finnhub", "ConcurrentTest", "test_key");

            // Act - 模擬並發更新
            var tasks = new List<Task>();
            for (int i = 0; i < 5; i++)
            {
                var taskIndex = i;
                tasks.Add(Task.Run(async () =>
                {
                    using var scope = ServiceProvider.CreateScope();
                    var context = scope.ServiceProvider.GetRequiredService<Net9QueryStockServerContext>();
                    
                    var usage = new ApiKeyUsage
                    {
                        Id = Guid.NewGuid(),
                        ApiKeyId = testKey.Id,
                        Endpoint = $"/test{taskIndex}",
                        IsSuccess = true,
                        ResponseTimeMs = 100 + taskIndex,
                        RequestTime = DateTimeOffset.UtcNow,
                        HttpStatusCode = 200
                    };
                    
                    context.ApiKeyUsages.Add(usage);
                    await context.SaveChangesAsync();
                }));
            }

            await Task.WhenAll(tasks);

            // Assert
            var usageCount = await Context.ApiKeyUsages.CountAsync(u => u.ApiKeyId == testKey.Id);
            Assert.Equal(5, usageCount);
        }

        [Fact]
        public async Task IndexPerformance_QueriesExecuteEfficiently()
        {
            // Arrange
            await CleanupTestDataAsync();
            await SeedTestApiKeyProviderAsync();
            
            // 建立大量測試資料
            var testKeys = new List<ApiKey>();
            var provider = await Context.ApiKeyProviders.FirstAsync(p => p.Name == "Finnhub");
            
            for (int i = 0; i < 100; i++)
            {
                testKeys.Add(new ApiKey
                {
                    Id = Guid.NewGuid(),
                    Name = $"PerfTestKey{i}",
                    EncryptedKey = $"encrypted_key_{i}",
                    ProviderId = provider.Id,
                    Priority = i % 10,
                    RateLimitPerMinute = 60,
                    RateLimitPerDay = 1000,
                    Status = i % 2 == 0 ? ApiKeyStatus.Active : ApiKeyStatus.Disabled,
                    CreatedAt = DateTimeOffset.UtcNow.AddDays(-i),
                    UpdatedAt = DateTimeOffset.UtcNow.AddDays(-i)
                });
            }
            
            Context.ApiKeys.AddRange(testKeys);
            await Context.SaveChangesAsync();

            // Act & Assert - 測試常見查詢的效能
            var stopwatch = System.Diagnostics.Stopwatch.StartNew();
            
            // 按狀態查詢
            var activeKeys = await Context.ApiKeys.Where(k => k.Status == ApiKeyStatus.Active).ToListAsync();
            
            // 按提供者查詢
            var finnhubKeys = await Context.ApiKeys.Where(k => k.Provider.Name == "Finnhub").ToListAsync();
            
            // 按優先級排序查詢
            var sortedKeys = await Context.ApiKeys.OrderBy(k => k.Priority).Take(10).ToListAsync();
            
            stopwatch.Stop();
            
            // 驗證查詢結果正確
            Assert.True(activeKeys.Count >= 50); // 應該有約 50 個啟用的金鑰
            Assert.True(finnhubKeys.Count >= 100); // 所有金鑰都屬於 Finnhub
            Assert.Equal(10, sortedKeys.Count);
            
            // 驗證查詢效能（應該在合理時間內完成）
            Assert.True(stopwatch.ElapsedMilliseconds < 5000, $"查詢耗時過長: {stopwatch.ElapsedMilliseconds}ms");
        }
    }
}
