using Net9.QueryStock.Server.Models;

namespace Net9.QueryStock.Server.Interfaces
{
    /// <summary>
    /// API 金鑰池服務介面
    /// </summary>
    public interface IApiKeyPoolService
    {
        /// <summary>
        /// 獲取可用的 API 金鑰
        /// </summary>
        /// <param name="providerName">服務提供者名稱</param>
        /// <param name="cancellationToken">取消權杖</param>
        /// <returns>可用的 API 金鑰，如果沒有可用的則返回 null</returns>
        Task<string?> GetAvailableApiKeyAsync(string providerName, CancellationToken cancellationToken = default);
        
        /// <summary>
        /// 記錄 API 金鑰使用情況
        /// </summary>
        /// <param name="providerName">服務提供者名稱</param>
        /// <param name="apiKey">使用的 API 金鑰</param>
        /// <param name="endpoint">請求的端點</param>
        /// <param name="isSuccess">是否成功</param>
        /// <param name="responseTimeMs">回應時間（毫秒）</param>
        /// <param name="errorMessage">錯誤訊息（如果有）</param>
        /// <param name="httpStatusCode">HTTP 狀態碼</param>
        /// <param name="cancellationToken">取消權杖</param>
        /// <returns>記錄結果</returns>
        Task<bool> RecordUsageAsync(
            string providerName,
            string apiKey,
            string endpoint,
            bool isSuccess,
            int responseTimeMs,
            string? errorMessage = null,
            int? httpStatusCode = null,
            CancellationToken cancellationToken = default);
        
        /// <summary>
        /// 標記 API 金鑰為錯誤狀態
        /// </summary>
        /// <param name="providerName">服務提供者名稱</param>
        /// <param name="apiKey">API 金鑰</param>
        /// <param name="errorMessage">錯誤訊息</param>
        /// <param name="suspendDurationMinutes">暫停時間（分鐘），如果為 null 則根據錯誤次數自動計算</param>
        /// <param name="cancellationToken">取消權杖</param>
        /// <returns>處理結果</returns>
        Task<bool> MarkKeyAsErrorAsync(
            string providerName,
            string apiKey,
            string errorMessage,
            int? suspendDurationMinutes = null,
            CancellationToken cancellationToken = default);
        
        /// <summary>
        /// 重置 API 金鑰錯誤狀態
        /// </summary>
        /// <param name="providerName">服務提供者名稱</param>
        /// <param name="apiKey">API 金鑰</param>
        /// <param name="cancellationToken">取消權杖</param>
        /// <returns>處理結果</returns>
        Task<bool> ResetKeyErrorStatusAsync(
            string providerName,
            string apiKey,
            CancellationToken cancellationToken = default);
        
        /// <summary>
        /// 獲取服務提供者的統計資訊
        /// </summary>
        /// <param name="providerName">服務提供者名稱</param>
        /// <param name="cancellationToken">取消權杖</param>
        /// <returns>統計資訊</returns>
        Task<ApiKeyPoolStats?> GetProviderStatsAsync(string providerName, CancellationToken cancellationToken = default);
        
        /// <summary>
        /// 獲取所有服務提供者的統計資訊
        /// </summary>
        /// <param name="cancellationToken">取消權杖</param>
        /// <returns>統計資訊列表</returns>
        Task<IEnumerable<ApiKeyPoolStats>> GetAllProvidersStatsAsync(CancellationToken cancellationToken = default);
        
        /// <summary>
        /// 新增 API 金鑰
        /// </summary>
        /// <param name="providerName">服務提供者名稱</param>
        /// <param name="keyName">金鑰名稱</param>
        /// <param name="plainKey">明文金鑰</param>
        /// <param name="priority">優先級</param>
        /// <param name="rateLimitPerMinute">每分鐘限制</param>
        /// <param name="rateLimitPerDay">每日限制</param>
        /// <param name="expiresAt">過期時間</param>
        /// <param name="cancellationToken">取消權杖</param>
        /// <returns>新增結果</returns>
        Task<bool> AddApiKeyAsync(
            string providerName,
            string keyName,
            string plainKey,
            int priority = 1,
            int? rateLimitPerMinute = null,
            int? rateLimitPerDay = null,
            DateTimeOffset? expiresAt = null,
            CancellationToken cancellationToken = default);
        
        /// <summary>
        /// 更新 API 金鑰狀態
        /// </summary>
        /// <param name="keyId">金鑰 ID</param>
        /// <param name="status">新狀態</param>
        /// <param name="cancellationToken">取消權杖</param>
        /// <returns>更新結果</returns>
        Task<bool> UpdateKeyStatusAsync(Guid keyId, ApiKeyStatus status, CancellationToken cancellationToken = default);
        
        /// <summary>
        /// 刪除 API 金鑰
        /// </summary>
        /// <param name="keyId">金鑰 ID</param>
        /// <param name="cancellationToken">取消權杖</param>
        /// <returns>刪除結果</returns>
        Task<bool> DeleteApiKeyAsync(Guid keyId, CancellationToken cancellationToken = default);
    }
    
    /// <summary>
    /// API 金鑰池統計資訊
    /// </summary>
    public class ApiKeyPoolStats
    {
        /// <summary>
        /// 服務提供者名稱
        /// </summary>
        public string ProviderName { get; set; } = string.Empty;
        
        /// <summary>
        /// 總金鑰數量
        /// </summary>
        public int TotalKeys { get; set; }
        
        /// <summary>
        /// 可用金鑰數量
        /// </summary>
        public int AvailableKeys { get; set; }
        
        /// <summary>
        /// 暫停的金鑰數量
        /// </summary>
        public int SuspendedKeys { get; set; }
        
        /// <summary>
        /// 已停用的金鑰數量
        /// </summary>
        public int DisabledKeys { get; set; }
        
        /// <summary>
        /// 今日總使用次數
        /// </summary>
        public long TodayUsageCount { get; set; }
        
        /// <summary>
        /// 當前分鐘使用次數
        /// </summary>
        public int CurrentMinuteUsageCount { get; set; }
        
        /// <summary>
        /// 平均回應時間（毫秒）
        /// </summary>
        public double AverageResponseTimeMs { get; set; }
        
        /// <summary>
        /// 成功率（百分比）
        /// </summary>
        public double SuccessRate { get; set; }
        
        /// <summary>
        /// 最後使用時間
        /// </summary>
        public DateTimeOffset? LastUsedAt { get; set; }
    }
}
