using System.ComponentModel.DataAnnotations;

namespace Net9.QueryStock.Server.Models
{
    /// <summary>
    /// API 服務提供者實體
    /// </summary>
    public class ApiKeyProvider
    {
        public Guid Id { get; set; }
        
        /// <summary>
        /// 提供者名稱 (例如: Finnhub, AlphaVantage, YahooFinance)
        /// </summary>
        [Required]
        [StringLength(50)]
        public string Name { get; set; } = string.Empty;
        
        /// <summary>
        /// 提供者顯示名稱
        /// </summary>
        [Required]
        [StringLength(100)]
        public string DisplayName { get; set; } = string.Empty;
        
        /// <summary>
        /// 提供者描述
        /// </summary>
        [StringLength(500)]
        public string? Description { get; set; }
        
        /// <summary>
        /// 基礎 URL
        /// </summary>
        [Required]
        [StringLength(200)]
        public string BaseUrl { get; set; } = string.Empty;
        
        /// <summary>
        /// 每分鐘請求限制
        /// </summary>
        public int RateLimitPerMinute { get; set; } = 60;
        
        /// <summary>
        /// 每日請求限制
        /// </summary>
        public int RateLimitPerDay { get; set; } = 1000;
        
        /// <summary>
        /// 是否啟用
        /// </summary>
        public bool IsEnabled { get; set; } = true;
        
        /// <summary>
        /// 優先級 (數字越小優先級越高)
        /// </summary>
        public int Priority { get; set; } = 1;
        
        /// <summary>
        /// 建立時間
        /// </summary>
        public DateTimeOffset CreatedAt { get; set; } = DateTimeOffset.UtcNow;
        
        /// <summary>
        /// 最後更新時間
        /// </summary>
        public DateTimeOffset UpdatedAt { get; set; } = DateTimeOffset.UtcNow;
        
        /// <summary>
        /// 關聯的 API 金鑰列表
        /// </summary>
        public virtual ICollection<ApiKey> ApiKeys { get; set; } = new List<ApiKey>();
    }
}
