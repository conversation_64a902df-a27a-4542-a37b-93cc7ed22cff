﻿// <auto-generated />
using System;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using Net9.QueryStock.Server.Data;

#nullable disable

namespace Net9.QueryStock.Server.Migrations
{
    [DbContext(typeof(Net9QueryStockServerContext))]
    [Migration("20250718103351_AddUserAuthentication")]
    partial class AddUserAuthentication
    {
        /// <inheritdoc />
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "9.0.7")
                .HasAnnotation("Relational:MaxIdentifierLength", 128);

            SqlServerModelBuilderExtensions.UseIdentityColumns(modelBuilder);

            modelBuilder.Entity("Net9.QueryStock.Server.Models.ApiKey", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<int>("ConsecutiveErrorCount")
                        .HasColumnType("int");

                    b.Property<DateTimeOffset>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetimeoffset")
                        .HasDefaultValueSql("GETUTCDATE()");

                    b.Property<DateTimeOffset>("CurrentMinuteStartTime")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetimeoffset")
                        .HasDefaultValueSql("GETUTCDATE()");

                    b.Property<int>("CurrentMinuteUsageCount")
                        .HasColumnType("int");

                    b.Property<string>("EncryptedKey")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<DateTimeOffset?>("ExpiresAt")
                        .HasColumnType("datetimeoffset");

                    b.Property<DateTimeOffset?>("LastErrorAt")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("LastErrorMessage")
                        .HasMaxLength(1000)
                        .HasColumnType("nvarchar(1000)");

                    b.Property<DateTimeOffset?>("LastUsedAt")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<int>("Priority")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasDefaultValue(1);

                    b.Property<Guid>("ProviderId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<int?>("RateLimitPerDay")
                        .HasColumnType("int");

                    b.Property<int?>("RateLimitPerMinute")
                        .HasColumnType("int");

                    b.Property<int>("Status")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasDefaultValue(1);

                    b.Property<DateTimeOffset?>("SuspendedUntil")
                        .HasColumnType("datetimeoffset");

                    b.Property<int>("TodayUsageCount")
                        .HasColumnType("int");

                    b.Property<long>("TotalUsageCount")
                        .HasColumnType("bigint");

                    b.Property<DateTimeOffset>("UpdatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetimeoffset")
                        .HasDefaultValueSql("GETUTCDATE()");

                    b.HasKey("Id");

                    b.HasIndex("ProviderId", "Name")
                        .IsUnique();

                    b.ToTable("ApiKeys");
                });

            modelBuilder.Entity("Net9.QueryStock.Server.Models.ApiKeyProvider", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("BaseUrl")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<DateTimeOffset>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetimeoffset")
                        .HasDefaultValueSql("GETUTCDATE()");

                    b.Property<string>("Description")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<string>("DisplayName")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<bool>("IsEnabled")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(true);

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<int>("Priority")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasDefaultValue(1);

                    b.Property<int>("RateLimitPerDay")
                        .HasColumnType("int");

                    b.Property<int>("RateLimitPerMinute")
                        .HasColumnType("int");

                    b.Property<DateTimeOffset>("UpdatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetimeoffset")
                        .HasDefaultValueSql("GETUTCDATE()");

                    b.HasKey("Id");

                    b.HasIndex("Name")
                        .IsUnique();

                    b.ToTable("ApiKeyProviders");
                });

            modelBuilder.Entity("Net9.QueryStock.Server.Models.ApiKeyUsage", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("ApiKeyId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Endpoint")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<string>("ErrorMessage")
                        .HasMaxLength(1000)
                        .HasColumnType("nvarchar(1000)");

                    b.Property<int?>("HttpStatusCode")
                        .HasColumnType("int");

                    b.Property<bool>("IsSuccess")
                        .HasColumnType("bit");

                    b.Property<string>("Metadata")
                        .HasMaxLength(2000)
                        .HasColumnType("nvarchar(2000)");

                    b.Property<string>("RequestParameters")
                        .HasMaxLength(2000)
                        .HasColumnType("nvarchar(2000)");

                    b.Property<DateTimeOffset>("RequestTime")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetimeoffset")
                        .HasDefaultValueSql("GETUTCDATE()");

                    b.Property<int>("ResponseTimeMs")
                        .HasColumnType("int");

                    b.Property<string>("UserAgent")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<string>("UserIpAddress")
                        .HasMaxLength(45)
                        .HasColumnType("nvarchar(45)");

                    b.HasKey("Id");

                    b.HasIndex("ApiKeyId");

                    b.HasIndex("RequestTime");

                    b.HasIndex("ApiKeyId", "RequestTime");

                    b.ToTable("ApiKeyUsages");
                });

            modelBuilder.Entity("Net9.QueryStock.Server.Models.RealTimeQuote", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<decimal?>("AskPrice")
                        .HasColumnType("decimal(18,4)");

                    b.Property<long?>("AskSize")
                        .HasColumnType("bigint");

                    b.Property<decimal?>("BidPrice")
                        .HasColumnType("decimal(18,4)");

                    b.Property<long?>("BidSize")
                        .HasColumnType("bigint");

                    b.Property<decimal>("Change")
                        .HasColumnType("decimal(18,4)");

                    b.Property<decimal>("ChangePercent")
                        .HasColumnType("decimal(8,4)");

                    b.Property<DateTimeOffset>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetimeoffset")
                        .HasDefaultValueSql("GETUTCDATE()");

                    b.Property<decimal>("CurrentPrice")
                        .HasColumnType("decimal(18,4)");

                    b.Property<string>("DataSource")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<decimal?>("HighPrice")
                        .HasColumnType("decimal(18,4)");

                    b.Property<decimal?>("LowPrice")
                        .HasColumnType("decimal(18,4)");

                    b.Property<string>("MarketStatus")
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.Property<decimal?>("OpenPrice")
                        .HasColumnType("decimal(18,4)");

                    b.Property<decimal?>("PreviousClose")
                        .HasColumnType("decimal(18,4)");

                    b.Property<DateTimeOffset>("QuoteTime")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("Symbol")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.Property<long?>("Volume")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("QuoteTime");

                    b.HasIndex("Symbol");

                    b.ToTable("RealTimeQuotes");
                });

            modelBuilder.Entity("Net9.QueryStock.Server.Models.Stock", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Country")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<DateTimeOffset>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetimeoffset")
                        .HasDefaultValueSql("GETUTCDATE()");

                    b.Property<string>("Currency")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(10)
                        .HasColumnType("nvarchar(10)")
                        .HasDefaultValue("USD");

                    b.Property<string>("Exchange")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("Industry")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<bool>("IsActive")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(true);

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<string>("StockType")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("Symbol")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.Property<DateTimeOffset>("UpdatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetimeoffset")
                        .HasDefaultValueSql("GETUTCDATE()");

                    b.HasKey("Id");

                    b.HasIndex("Symbol")
                        .IsUnique();

                    b.ToTable("Stock");
                });

            modelBuilder.Entity("Net9.QueryStock.Server.Models.StockInfo", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<long?>("AverageVolume200Day")
                        .HasColumnType("bigint");

                    b.Property<long?>("AverageVolume50Day")
                        .HasColumnType("bigint");

                    b.Property<string>("Description")
                        .HasMaxLength(2000)
                        .HasColumnType("nvarchar(2000)");

                    b.Property<decimal?>("DividendYield")
                        .HasColumnType("decimal(5,4)");

                    b.Property<decimal?>("EarningsPerShare")
                        .HasColumnType("decimal(10,4)");

                    b.Property<int?>("EmployeeCount")
                        .HasColumnType("int");

                    b.Property<DateOnly?>("FoundedDate")
                        .HasColumnType("date");

                    b.Property<string>("Headquarters")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<DateOnly?>("IpoDate")
                        .HasColumnType("date");

                    b.Property<decimal?>("MarketCapitalization")
                        .HasColumnType("decimal(18,2)");

                    b.Property<decimal?>("PbRatio")
                        .HasColumnType("decimal(10,2)");

                    b.Property<decimal?>("PeRatio")
                        .HasColumnType("decimal(10,2)");

                    b.Property<long?>("SharesOutstanding")
                        .HasColumnType("bigint");

                    b.Property<Guid>("StockId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTimeOffset>("UpdatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetimeoffset")
                        .HasDefaultValueSql("GETUTCDATE()");

                    b.Property<string>("Website")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<decimal?>("Week52High")
                        .HasColumnType("decimal(18,4)");

                    b.Property<decimal?>("Week52Low")
                        .HasColumnType("decimal(18,4)");

                    b.HasKey("Id");

                    b.HasIndex("StockId")
                        .IsUnique();

                    b.ToTable("StockInfos");
                });

            modelBuilder.Entity("Net9.QueryStock.Server.Models.StockPrice", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<decimal?>("AdjustedClosePrice")
                        .HasColumnType("decimal(18,4)");

                    b.Property<decimal>("ClosePrice")
                        .HasColumnType("decimal(18,4)");

                    b.Property<DateTimeOffset>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetimeoffset")
                        .HasDefaultValueSql("GETUTCDATE()");

                    b.Property<string>("DataSource")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<decimal>("HighPrice")
                        .HasColumnType("decimal(18,4)");

                    b.Property<decimal>("LowPrice")
                        .HasColumnType("decimal(18,4)");

                    b.Property<decimal>("OpenPrice")
                        .HasColumnType("decimal(18,4)");

                    b.Property<DateOnly>("PriceDate")
                        .HasColumnType("date");

                    b.Property<Guid>("StockId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<long>("Volume")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("StockId", "PriceDate")
                        .IsUnique();

                    b.ToTable("StockPrices");
                });

            modelBuilder.Entity("Net9.QueryStock.Server.Models.User", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTimeOffset>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetimeoffset")
                        .HasDefaultValueSql("GETUTCDATE()");

                    b.Property<string>("Email")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<bool>("IsActive")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(true);

                    b.Property<DateTimeOffset?>("LastLoginAt")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("PasswordHash")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<string>("Role")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)")
                        .HasDefaultValue("User");

                    b.Property<string>("Salt")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<DateTimeOffset>("UpdatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetimeoffset")
                        .HasDefaultValueSql("GETUTCDATE()");

                    b.Property<string>("Username")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.HasKey("Id");

                    b.HasIndex("Email")
                        .IsUnique()
                        .HasFilter("[Email] IS NOT NULL");

                    b.HasIndex("Username")
                        .IsUnique();

                    b.ToTable("Users");
                });

            modelBuilder.Entity("Net9.QueryStock.Server.Models.ApiKey", b =>
                {
                    b.HasOne("Net9.QueryStock.Server.Models.ApiKeyProvider", "Provider")
                        .WithMany("ApiKeys")
                        .HasForeignKey("ProviderId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Provider");
                });

            modelBuilder.Entity("Net9.QueryStock.Server.Models.ApiKeyUsage", b =>
                {
                    b.HasOne("Net9.QueryStock.Server.Models.ApiKey", "ApiKey")
                        .WithMany("UsageRecords")
                        .HasForeignKey("ApiKeyId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("ApiKey");
                });

            modelBuilder.Entity("Net9.QueryStock.Server.Models.StockInfo", b =>
                {
                    b.HasOne("Net9.QueryStock.Server.Models.Stock", "Stock")
                        .WithOne("StockInfo")
                        .HasForeignKey("Net9.QueryStock.Server.Models.StockInfo", "StockId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Stock");
                });

            modelBuilder.Entity("Net9.QueryStock.Server.Models.StockPrice", b =>
                {
                    b.HasOne("Net9.QueryStock.Server.Models.Stock", "Stock")
                        .WithMany("StockPrices")
                        .HasForeignKey("StockId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Stock");
                });

            modelBuilder.Entity("Net9.QueryStock.Server.Models.ApiKey", b =>
                {
                    b.Navigation("UsageRecords");
                });

            modelBuilder.Entity("Net9.QueryStock.Server.Models.ApiKeyProvider", b =>
                {
                    b.Navigation("ApiKeys");
                });

            modelBuilder.Entity("Net9.QueryStock.Server.Models.Stock", b =>
                {
                    b.Navigation("StockInfo");

                    b.Navigation("StockPrices");
                });
#pragma warning restore 612, 618
        }
    }
}
