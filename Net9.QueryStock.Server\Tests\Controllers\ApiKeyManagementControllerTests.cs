using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Mvc.Testing;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Net9.QueryStock.Server.Data;
using Net9.QueryStock.Server.DTOs;
using Net9.QueryStock.Server.Services;
using System.Net.Http.Json;
using System.Text.Json;
using Xunit;

namespace Net9.QueryStock.Server.Tests.Controllers
{
    /// <summary>
    /// API 金鑰管理控制器整合測試
    /// 測試金鑰管理的完整 HTTP 流程
    /// </summary>
    public class ApiKeyManagementControllerTests : IClassFixture<WebApplicationFactory<Program>>
    {
        private readonly WebApplicationFactory<Program> _factory;
        private readonly HttpClient _client;

        public ApiKeyManagementControllerTests(WebApplicationFactory<Program> factory)
        {
            _factory = factory.WithWebHostBuilder(builder =>
            {
                builder.UseEnvironment("Testing");
                builder.ConfigureServices(services =>
                {
                    // 使用測試資料庫
                    var descriptor = services.SingleOrDefault(d => d.ServiceType == typeof(DbContextOptions<Net9QueryStockServerContext>));
                    if (descriptor != null)
                    {
                        services.Remove(descriptor);
                    }

                    services.AddDbContext<Net9QueryStockServerContext>(options =>
                    {
                        options.UseSqlServer("Server=(localdb)\\mssqllocaldb;Database=Net9QueryStockApiKeyTest;Trusted_Connection=true;MultipleActiveResultSets=true");
                    });
                });
            });

            _client = _factory.CreateClient();
            
            // 確保測試資料庫存在並初始化種子資料
            using var scope = _factory.Services.CreateScope();
            var context = scope.ServiceProvider.GetRequiredService<Net9QueryStockServerContext>();
            context.Database.EnsureCreated();
            
            var seeder = scope.ServiceProvider.GetRequiredService<DatabaseSeederService>();
            seeder.SeedAsync().Wait();
        }

        [Fact]
        public async Task GetProviders_ReturnsAllProviders()
        {
            // Arrange
            await CleanupTestData();

            // Act
            var response = await _client.GetAsync("/api/apikeymanagement/providers");

            // Assert
            response.EnsureSuccessStatusCode();
            var content = await response.Content.ReadAsStringAsync();
            var providers = JsonSerializer.Deserialize<List<object>>(content, new JsonSerializerOptions { PropertyNameCaseInsensitive = true });
            
            Assert.NotNull(providers);
            Assert.True(providers.Count >= 1); // 至少應該有 Finnhub 提供者
        }

        [Fact]
        public async Task GetProviderStats_WithValidProvider_ReturnsStats()
        {
            // Arrange
            await CleanupTestData();
            await AddTestApiKey();

            // Act
            var response = await _client.GetAsync("/api/apikeymanagement/providers/Finnhub/stats");

            // Assert
            response.EnsureSuccessStatusCode();
            var content = await response.Content.ReadAsStringAsync();
            var stats = JsonSerializer.Deserialize<object>(content, new JsonSerializerOptions { PropertyNameCaseInsensitive = true });
            
            Assert.NotNull(stats);
        }

        [Fact]
        public async Task GetProviderStats_WithInvalidProvider_ReturnsNotFound()
        {
            // Arrange
            await CleanupTestData();

            // Act
            var response = await _client.GetAsync("/api/apikeymanagement/providers/NonExistentProvider/stats");

            // Assert
            Assert.Equal(System.Net.HttpStatusCode.NotFound, response.StatusCode);
        }

        [Fact]
        public async Task AddApiKey_WithValidData_ReturnsSuccess()
        {
            // Arrange
            await CleanupTestData();
            var addKeyRequest = new
            {
                keyName = "TestApiKey",
                apiKey = "test_api_key_12345",
                priority = 1,
                rateLimitPerMinute = 60,
                rateLimitPerDay = 1000
            };

            // Act
            var response = await _client.PostAsJsonAsync("/api/apikeymanagement/providers/Finnhub/keys", addKeyRequest);

            // Assert
            response.EnsureSuccessStatusCode();
            var content = await response.Content.ReadAsStringAsync();
            
            // 驗證金鑰已添加
            var providersResponse = await _client.GetAsync("/api/apikeymanagement/providers");
            var providersContent = await providersResponse.Content.ReadAsStringAsync();
            Assert.Contains("TestApiKey", providersContent);
        }

        [Fact]
        public async Task AddApiKey_WithInvalidProvider_ReturnsNotFound()
        {
            // Arrange
            await CleanupTestData();
            var addKeyRequest = new
            {
                keyName = "TestApiKey",
                apiKey = "test_api_key_12345",
                priority = 1,
                rateLimitPerMinute = 60,
                rateLimitPerDay = 1000
            };

            // Act
            var response = await _client.PostAsJsonAsync("/api/apikeymanagement/providers/InvalidProvider/keys", addKeyRequest);

            // Assert
            Assert.Equal(System.Net.HttpStatusCode.NotFound, response.StatusCode);
        }

        [Fact]
        public async Task AddApiKey_WithDuplicateName_ReturnsBadRequest()
        {
            // Arrange
            await CleanupTestData();
            await AddTestApiKey("DuplicateKey");

            var addKeyRequest = new
            {
                keyName = "DuplicateKey",
                apiKey = "different_api_key_12345",
                priority = 1,
                rateLimitPerMinute = 60,
                rateLimitPerDay = 1000
            };

            // Act
            var response = await _client.PostAsJsonAsync("/api/apikeymanagement/providers/Finnhub/keys", addKeyRequest);

            // Assert
            Assert.Equal(System.Net.HttpStatusCode.BadRequest, response.StatusCode);
        }

        [Fact]
        public async Task UpdateApiKeyStatus_WithValidKey_ReturnsSuccess()
        {
            // Arrange
            await CleanupTestData();
            var keyId = await AddTestApiKey();

            var updateRequest = new
            {
                status = "Disabled"
            };

            // Act
            var response = await _client.PutAsJsonAsync($"/api/apikeymanagement/keys/{keyId}/status", updateRequest);

            // Assert
            response.EnsureSuccessStatusCode();
        }

        [Fact]
        public async Task UpdateApiKeyStatus_WithInvalidKey_ReturnsNotFound()
        {
            // Arrange
            await CleanupTestData();
            var invalidKeyId = Guid.NewGuid();

            var updateRequest = new
            {
                status = "Disabled"
            };

            // Act
            var response = await _client.PutAsJsonAsync($"/api/apikeymanagement/keys/{invalidKeyId}/status", updateRequest);

            // Assert
            Assert.Equal(System.Net.HttpStatusCode.NotFound, response.StatusCode);
        }

        [Fact]
        public async Task DeleteApiKey_WithValidKey_ReturnsSuccess()
        {
            // Arrange
            await CleanupTestData();
            var keyId = await AddTestApiKey();

            // Act
            var response = await _client.DeleteAsync($"/api/apikeymanagement/keys/{keyId}");

            // Assert
            response.EnsureSuccessStatusCode();
        }

        [Fact]
        public async Task DeleteApiKey_WithInvalidKey_ReturnsNotFound()
        {
            // Arrange
            await CleanupTestData();
            var invalidKeyId = Guid.NewGuid();

            // Act
            var response = await _client.DeleteAsync($"/api/apikeymanagement/keys/{invalidKeyId}");

            // Assert
            Assert.Equal(System.Net.HttpStatusCode.NotFound, response.StatusCode);
        }

        [Fact]
        public async Task GetAllStats_ReturnsStatsForAllProviders()
        {
            // Arrange
            await CleanupTestData();
            await AddTestApiKey();

            // Act
            var response = await _client.GetAsync("/api/apikeymanagement/stats");

            // Assert
            response.EnsureSuccessStatusCode();
            var content = await response.Content.ReadAsStringAsync();
            var stats = JsonSerializer.Deserialize<List<object>>(content, new JsonSerializerOptions { PropertyNameCaseInsensitive = true });
            
            Assert.NotNull(stats);
            Assert.True(stats.Count >= 1);
        }

        [Fact]
        public async Task GetUsageHistory_WithValidProvider_ReturnsHistory()
        {
            // Arrange
            await CleanupTestData();
            await AddTestApiKey();

            // Act
            var response = await _client.GetAsync("/api/apikeymanagement/providers/Finnhub/usage?days=7");

            // Assert
            response.EnsureSuccessStatusCode();
            var content = await response.Content.ReadAsStringAsync();
            var history = JsonSerializer.Deserialize<List<object>>(content, new JsonSerializerOptions { PropertyNameCaseInsensitive = true });
            
            Assert.NotNull(history);
        }

        [Fact]
        public async Task GetUsageHistory_WithInvalidDays_ReturnsBadRequest()
        {
            // Arrange
            await CleanupTestData();

            // Act
            var response = await _client.GetAsync("/api/apikeymanagement/providers/Finnhub/usage?days=0");

            // Assert
            Assert.Equal(System.Net.HttpStatusCode.BadRequest, response.StatusCode);
        }

        [Fact]
        public async Task ApiKeyEncryption_WorksCorrectly()
        {
            // Arrange
            await CleanupTestData();
            var originalApiKey = "super_secret_api_key_12345";
            
            var addKeyRequest = new
            {
                keyName = "EncryptionTestKey",
                apiKey = originalApiKey,
                priority = 1,
                rateLimitPerMinute = 60,
                rateLimitPerDay = 1000
            };

            // Act
            var response = await _client.PostAsJsonAsync("/api/apikeymanagement/providers/Finnhub/keys", addKeyRequest);

            // Assert
            response.EnsureSuccessStatusCode();

            // 驗證金鑰在資料庫中是加密的
            using var scope = _factory.Services.CreateScope();
            var context = scope.ServiceProvider.GetRequiredService<Net9QueryStockServerContext>();
            var storedKey = context.ApiKeys.FirstOrDefault(k => k.Name == "EncryptionTestKey");
            
            Assert.NotNull(storedKey);
            Assert.NotEqual(originalApiKey, storedKey.EncryptedKey); // 應該是加密的
            Assert.NotEmpty(storedKey.EncryptedKey);
        }

        private async Task CleanupTestData()
        {
            using var scope = _factory.Services.CreateScope();
            var context = scope.ServiceProvider.GetRequiredService<Net9QueryStockServerContext>();
            
            context.ApiKeyUsages.RemoveRange(context.ApiKeyUsages);
            context.ApiKeys.RemoveRange(context.ApiKeys);
            await context.SaveChangesAsync();
        }

        private async Task<Guid> AddTestApiKey(string keyName = "TestKey")
        {
            var addKeyRequest = new
            {
                keyName = keyName,
                apiKey = "test_api_key_12345",
                priority = 1,
                rateLimitPerMinute = 60,
                rateLimitPerDay = 1000
            };

            var response = await _client.PostAsJsonAsync("/api/apikeymanagement/providers/Finnhub/keys", addKeyRequest);
            response.EnsureSuccessStatusCode();

            // 獲取新建立的金鑰 ID
            using var scope = _factory.Services.CreateScope();
            var context = scope.ServiceProvider.GetRequiredService<Net9QueryStockServerContext>();
            var key = context.ApiKeys.FirstOrDefault(k => k.Name == keyName);
            
            return key?.Id ?? Guid.Empty;
        }
    }
}
