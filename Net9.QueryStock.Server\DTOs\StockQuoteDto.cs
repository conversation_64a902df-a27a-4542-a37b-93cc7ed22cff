namespace Net9.QueryStock.Server.DTOs
{
    /// <summary>
    /// 股票報價資料傳輸物件
    /// </summary>
    public class StockQuoteDto
    {
        /// <summary>
        /// 股票代號
        /// </summary>
        public string Symbol { get; set; } = string.Empty;
        
        /// <summary>
        /// 公司名稱
        /// </summary>
        public string Name { get; set; } = string.Empty;
        
        /// <summary>
        /// 當前價格
        /// </summary>
        public decimal CurrentPrice { get; set; }
        
        /// <summary>
        /// 價格變動
        /// </summary>
        public decimal Change { get; set; }
        
        /// <summary>
        /// 價格變動百分比
        /// </summary>
        public decimal ChangePercent { get; set; }
        
        /// <summary>
        /// 開盤價
        /// </summary>
        public decimal? OpenPrice { get; set; }
        
        /// <summary>
        /// 最高價
        /// </summary>
        public decimal? HighPrice { get; set; }
        
        /// <summary>
        /// 最低價
        /// </summary>
        public decimal? LowPrice { get; set; }
        
        /// <summary>
        /// 前一交易日收盤價
        /// </summary>
        public decimal? PreviousClose { get; set; }
        
        /// <summary>
        /// 成交量
        /// </summary>
        public long? Volume { get; set; }
        
        /// <summary>
        /// 市場狀態
        /// </summary>
        public string? MarketStatus { get; set; }
        
        /// <summary>
        /// 報價時間
        /// </summary>
        public DateTimeOffset QuoteTime { get; set; }
        
        /// <summary>
        /// 資料來源
        /// </summary>
        public string DataSource { get; set; } = string.Empty;
    }
}
