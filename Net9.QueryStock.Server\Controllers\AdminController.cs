using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Net9.QueryStock.Server.Interfaces;
using Net9.QueryStock.Server.Services;

namespace Net9.QueryStock.Server.Controllers
{
    /// <summary>
    /// 管理員控制器
    /// </summary>
    [Route("api/[controller]")]
    [ApiController]
    public class AdminController : ControllerBase
    {
        private readonly IApiKeyPoolService _apiKeyPoolService;
        private readonly DatabaseSeederService _seederService;
        private readonly ILogger<AdminController> _logger;

        public AdminController(
            IApiKeyPoolService apiKeyPoolService,
            DatabaseSeederService seederService,
            ILogger<AdminController> logger)
        {
            _apiKeyPoolService = apiKeyPoolService;
            _seederService = seederService;
            _logger = logger;
        }

        /// <summary>
        /// 快速添加 Finnhub API 金鑰（用於初始設定）
        /// </summary>
        /// <param name="request">金鑰資訊</param>
        /// <returns>添加結果</returns>
        [HttpPost("setup/finnhub-key")]
        public async Task<ActionResult> SetupFinnhubKey([FromBody] SetupKeyRequest request)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(request.ApiKey))
                {
                    return BadRequest(new { message = "API 金鑰不能為空" });
                }

                var success = await _apiKeyPoolService.AddApiKeyAsync(
                    "Finnhub",
                    request.KeyName ?? "Finnhub-Default-Key",
                    request.ApiKey,
                    1, // 優先級
                    60, // 每分鐘限制
                    1000 // 每日限制
                );

                if (success)
                {
                    _logger.LogInformation("成功添加 Finnhub API 金鑰: {KeyName}", request.KeyName);
                    return Ok(new { 
                        message = "Finnhub API 金鑰添加成功！現在可以開始使用股票查詢功能。",
                        keyName = request.KeyName ?? "Finnhub-Default-Key"
                    });
                }
                else
                {
                    return BadRequest(new { message = "添加 API 金鑰失敗，請檢查提供者是否存在" });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "設定 Finnhub API 金鑰時發生錯誤");
                return StatusCode(500, new { message = "設定 API 金鑰時發生內部錯誤" });
            }
        }

        /// <summary>
        /// 檢查系統狀態
        /// </summary>
        /// <returns>系統狀態資訊</returns>
        [HttpGet("status")]
        public async Task<ActionResult> GetSystemStatus()
        {
            try
            {
                var stats = await _apiKeyPoolService.GetAllProvidersStatsAsync();
                var statusInfo = new
                {
                    providers = stats.Select(s => new
                    {
                        name = s.ProviderName,
                        totalKeys = s.TotalKeys,
                        availableKeys = s.AvailableKeys,
                        isReady = s.AvailableKeys > 0
                    }).ToList(),
                    systemReady = stats.Any(s => s.AvailableKeys > 0),
                    message = stats.Any(s => s.AvailableKeys > 0) 
                        ? "系統已就緒，可以開始使用股票查詢功能" 
                        : "系統尚未配置 API 金鑰，請先添加至少一個 API 金鑰"
                };

                return Ok(statusInfo);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "獲取系統狀態時發生錯誤");
                return StatusCode(500, new { message = "獲取系統狀態時發生內部錯誤" });
            }
        }

        /// <summary>
        /// 重新初始化資料庫種子資料
        /// </summary>
        /// <returns>初始化結果</returns>
        [HttpPost("reseed")]
        public async Task<ActionResult> ReseedDatabase()
        {
            try
            {
                await _seederService.SeedAsync();
                return Ok(new { message = "資料庫種子資料重新初始化完成" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "重新初始化資料庫時發生錯誤");
                return StatusCode(500, new { message = "重新初始化資料庫時發生內部錯誤" });
            }
        }

        /// <summary>
        /// 獲取設定指南
        /// </summary>
        /// <returns>設定指南</returns>
        [HttpGet("setup-guide")]
        public ActionResult GetSetupGuide()
        {
            var guide = new
            {
                title = "股票查詢平台設定指南",
                steps = new object[]
                {
                    new
                    {
                        step = 1,
                        title = "註冊 Finnhub 帳號",
                        description = "前往 https://finnhub.io 註冊免費帳號",
                        details = "免費帳號每分鐘可查詢 60 次，每月 1000 次"
                    },
                    new
                    {
                        step = 2,
                        title = "獲取 API 金鑰",
                        description = "登入 Finnhub 後，在 Dashboard 頁面複製您的 API 金鑰",
                        details = "API 金鑰格式類似：c123456789abcdef"
                    },
                    new
                    {
                        step = 3,
                        title = "添加 API 金鑰到系統",
                        description = "使用以下 API 端點添加您的 Finnhub API 金鑰",
                        endpoint = "POST /api/admin/setup/finnhub-key",
                        example = new
                        {
                            keyName = "My-Finnhub-Key",
                            apiKey = "your-actual-api-key-here"
                        }
                    },
                    new
                    {
                        step = 4,
                        title = "開始使用",
                        description = "API 金鑰添加成功後，即可開始使用股票查詢功能",
                        details = "您可以在金鑰管理頁面查看和管理您的 API 金鑰"
                    }
                },
                apiEndpoints = new
                {
                    setupKey = "POST /api/admin/setup/finnhub-key",
                    checkStatus = "GET /api/admin/status",
                    keyManagement = "GET /api/apikeymanagement/providers"
                }
            };

            return Ok(guide);
        }
    }

    /// <summary>
    /// 設定金鑰請求
    /// </summary>
    public class SetupKeyRequest
    {
        /// <summary>
        /// 金鑰名稱
        /// </summary>
        public string? KeyName { get; set; }
        
        /// <summary>
        /// API 金鑰
        /// </summary>
        public string ApiKey { get; set; } = string.Empty;
    }
}
