# 股票查詢平台測試執行腳本
# 此腳本會執行所有單元測試和整合測試

Write-Host "=== 股票查詢平台測試執行 ===" -ForegroundColor Green
Write-Host ""

# 檢查 .NET 版本
Write-Host "檢查 .NET 版本..." -ForegroundColor Yellow
dotnet --version

# 還原套件
Write-Host ""
Write-Host "還原 NuGet 套件..." -ForegroundColor Yellow
dotnet restore

# 建置專案
Write-Host ""
Write-Host "建置專案..." -ForegroundColor Yellow
dotnet build --no-restore

if ($LASTEXITCODE -ne 0) {
    Write-Host "建置失敗！" -ForegroundColor Red
    exit 1
}

# 設定測試環境變數
$env:ASPNETCORE_ENVIRONMENT = "Testing"

Write-Host ""
Write-Host "=== 開始執行測試 ===" -ForegroundColor Green
Write-Host ""

# 執行所有測試
Write-Host "執行所有測試..." -ForegroundColor Yellow
dotnet test --no-build --verbosity normal --logger "console;verbosity=detailed"

$testResult = $LASTEXITCODE

Write-Host ""
if ($testResult -eq 0) {
    Write-Host "=== 所有測試通過！ ===" -ForegroundColor Green
} else {
    Write-Host "=== 部分測試失敗 ===" -ForegroundColor Red
}

Write-Host ""
Write-Host "測試分類說明：" -ForegroundColor Cyan
Write-Host "• 單元測試 (Unit Tests)：測試個別服務和組件的功能" -ForegroundColor White
Write-Host "• 整合測試 (Integration Tests)：測試資料庫操作和外部 API 調用" -ForegroundColor White
Write-Host "• 控制器測試 (Controller Tests)：測試完整的 HTTP 請求流程" -ForegroundColor White
Write-Host ""
Write-Host "注意事項：" -ForegroundColor Yellow
Write-Host "• 外部 API 測試需要實際的 Finnhub API 金鑰" -ForegroundColor White
Write-Host "• 資料庫測試使用實際的 SQL Server LocalDB" -ForegroundColor White
Write-Host "• 測試會自動清理測試資料" -ForegroundColor White

exit $testResult
