﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Net9.QueryStock.Server.Migrations
{
    /// <inheritdoc />
    public partial class InitialStockModels : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "RealTimeQuotes",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    Symbol = table.Column<string>(type: "nvarchar(20)", maxLength: 20, nullable: false),
                    CurrentPrice = table.Column<decimal>(type: "decimal(18,4)", nullable: false),
                    Change = table.Column<decimal>(type: "decimal(18,4)", nullable: false),
                    ChangePercent = table.Column<decimal>(type: "decimal(8,4)", nullable: false),
                    OpenPrice = table.Column<decimal>(type: "decimal(18,4)", nullable: true),
                    HighPrice = table.Column<decimal>(type: "decimal(18,4)", nullable: true),
                    LowPrice = table.Column<decimal>(type: "decimal(18,4)", nullable: true),
                    PreviousClose = table.Column<decimal>(type: "decimal(18,4)", nullable: true),
                    Volume = table.Column<long>(type: "bigint", nullable: true),
                    BidPrice = table.Column<decimal>(type: "decimal(18,4)", nullable: true),
                    AskPrice = table.Column<decimal>(type: "decimal(18,4)", nullable: true),
                    BidSize = table.Column<long>(type: "bigint", nullable: true),
                    AskSize = table.Column<long>(type: "bigint", nullable: true),
                    MarketStatus = table.Column<string>(type: "nvarchar(20)", maxLength: 20, nullable: true),
                    QuoteTime = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    DataSource = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    CreatedAt = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false, defaultValueSql: "GETUTCDATE()")
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_RealTimeQuotes", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "Stock",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    Symbol = table.Column<string>(type: "nvarchar(20)", maxLength: 20, nullable: false),
                    Name = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: false),
                    Exchange = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                    Currency = table.Column<string>(type: "nvarchar(10)", maxLength: 10, nullable: false, defaultValue: "USD"),
                    StockType = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                    Industry = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true),
                    Country = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                    IsActive = table.Column<bool>(type: "bit", nullable: false, defaultValue: true),
                    CreatedAt = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false, defaultValueSql: "GETUTCDATE()"),
                    UpdatedAt = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false, defaultValueSql: "GETUTCDATE()")
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Stock", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "StockInfos",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    StockId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    MarketCapitalization = table.Column<decimal>(type: "decimal(18,2)", nullable: true),
                    PeRatio = table.Column<decimal>(type: "decimal(10,2)", nullable: true),
                    PbRatio = table.Column<decimal>(type: "decimal(10,2)", nullable: true),
                    DividendYield = table.Column<decimal>(type: "decimal(5,4)", nullable: true),
                    EarningsPerShare = table.Column<decimal>(type: "decimal(10,4)", nullable: true),
                    Week52High = table.Column<decimal>(type: "decimal(18,4)", nullable: true),
                    Week52Low = table.Column<decimal>(type: "decimal(18,4)", nullable: true),
                    AverageVolume50Day = table.Column<long>(type: "bigint", nullable: true),
                    AverageVolume200Day = table.Column<long>(type: "bigint", nullable: true),
                    SharesOutstanding = table.Column<long>(type: "bigint", nullable: true),
                    Description = table.Column<string>(type: "nvarchar(2000)", maxLength: 2000, nullable: true),
                    Website = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true),
                    Headquarters = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true),
                    EmployeeCount = table.Column<int>(type: "int", nullable: true),
                    FoundedDate = table.Column<DateOnly>(type: "date", nullable: true),
                    IpoDate = table.Column<DateOnly>(type: "date", nullable: true),
                    UpdatedAt = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false, defaultValueSql: "GETUTCDATE()")
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_StockInfos", x => x.Id);
                    table.ForeignKey(
                        name: "FK_StockInfos_Stock_StockId",
                        column: x => x.StockId,
                        principalTable: "Stock",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "StockPrices",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    StockId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    OpenPrice = table.Column<decimal>(type: "decimal(18,4)", nullable: false),
                    HighPrice = table.Column<decimal>(type: "decimal(18,4)", nullable: false),
                    LowPrice = table.Column<decimal>(type: "decimal(18,4)", nullable: false),
                    ClosePrice = table.Column<decimal>(type: "decimal(18,4)", nullable: false),
                    AdjustedClosePrice = table.Column<decimal>(type: "decimal(18,4)", nullable: true),
                    Volume = table.Column<long>(type: "bigint", nullable: false),
                    PriceDate = table.Column<DateOnly>(type: "date", nullable: false),
                    DataSource = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    CreatedAt = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false, defaultValueSql: "GETUTCDATE()")
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_StockPrices", x => x.Id);
                    table.ForeignKey(
                        name: "FK_StockPrices_Stock_StockId",
                        column: x => x.StockId,
                        principalTable: "Stock",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "IX_RealTimeQuotes_QuoteTime",
                table: "RealTimeQuotes",
                column: "QuoteTime");

            migrationBuilder.CreateIndex(
                name: "IX_RealTimeQuotes_Symbol",
                table: "RealTimeQuotes",
                column: "Symbol");

            migrationBuilder.CreateIndex(
                name: "IX_Stock_Symbol",
                table: "Stock",
                column: "Symbol",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_StockInfos_StockId",
                table: "StockInfos",
                column: "StockId",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_StockPrices_StockId_PriceDate",
                table: "StockPrices",
                columns: new[] { "StockId", "PriceDate" },
                unique: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "RealTimeQuotes");

            migrationBuilder.DropTable(
                name: "StockInfos");

            migrationBuilder.DropTable(
                name: "StockPrices");

            migrationBuilder.DropTable(
                name: "Stock");
        }
    }
}
