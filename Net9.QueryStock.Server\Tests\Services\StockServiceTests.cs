using Microsoft.Extensions.DependencyInjection;
using Net9.QueryStock.Server.Interfaces;
using Xunit;

namespace Net9.QueryStock.Server.Tests.Services
{
    /// <summary>
    /// 股票服務測試
    /// 測試實際的外部 API 調用和股票資料處理
    /// 注意：這些測試需要實際的 API 金鑰才能通過
    /// </summary>
    public class StockServiceTests : TestBase
    {
        private readonly IStockService _stockService;

        public StockServiceTests()
        {
            _stockService = ServiceProvider.GetRequiredService<IStockService>();
        }

        [Fact]
        public async Task SearchStocksAsync_WithValidQuery_ReturnsResults()
        {
            // Arrange
            await CleanupTestDataAsync();
            await SeedTestApiKeyProviderAsync();
            
            // 注意：這個測試需要實際的 Finnhub API 金鑰
            // 如果沒有金鑰，測試會失敗，這是預期的行為
            await CreateTestApiKeyAsync("Finnhub", "TestKey", "YOUR_ACTUAL_FINNHUB_API_KEY_HERE");

            // Act
            var results = await _stockService.SearchStocksAsync("AAPL");

            // Assert
            // 如果有有效的 API 金鑰，應該返回結果
            // 如果沒有，會返回空結果或拋出異常
            Assert.NotNull(results);
            
            // 如果有結果，驗證結果格式
            if (results.Any())
            {
                var firstResult = results.First();
                Assert.NotNull(firstResult.Symbol);
                Assert.NotNull(firstResult.Description);
            }
        }

        [Fact]
        public async Task SearchStocksAsync_WithEmptyQuery_ReturnsEmptyResults()
        {
            // Arrange
            await CleanupTestDataAsync();
            await SeedTestApiKeyProviderAsync();
            await CreateTestApiKeyAsync("Finnhub", "TestKey", "test_key");

            // Act
            var results = await _stockService.SearchStocksAsync("");

            // Assert
            Assert.NotNull(results);
            Assert.Empty(results);
        }

        [Fact]
        public async Task SearchStocksAsync_WithNoApiKey_ReturnsEmptyResults()
        {
            // Arrange
            await CleanupTestDataAsync();
            await SeedTestApiKeyProviderAsync();
            // 不添加任何 API 金鑰

            // Act
            var results = await _stockService.SearchStocksAsync("AAPL");

            // Assert
            Assert.NotNull(results);
            Assert.Empty(results);
        }

        [Fact]
        public async Task GetRealTimeQuoteAsync_WithValidSymbol_ReturnsQuote()
        {
            // Arrange
            await CleanupTestDataAsync();
            await SeedTestApiKeyProviderAsync();
            
            // 注意：這個測試需要實際的 Finnhub API 金鑰
            await CreateTestApiKeyAsync("Finnhub", "TestKey", "YOUR_ACTUAL_FINNHUB_API_KEY_HERE");

            // Act
            var quote = await _stockService.GetRealTimeQuoteAsync("AAPL");

            // Assert
            if (quote != null) // 只有在有有效 API 金鑰時才會有結果
            {
                Assert.NotNull(quote.Symbol);
                Assert.True(quote.CurrentPrice >= 0);
                Assert.True(quote.QuoteTime > DateTimeOffset.MinValue);
            }
        }

        [Fact]
        public async Task GetRealTimeQuoteAsync_WithInvalidSymbol_ReturnsNull()
        {
            // Arrange
            await CleanupTestDataAsync();
            await SeedTestApiKeyProviderAsync();
            await CreateTestApiKeyAsync("Finnhub", "TestKey", "YOUR_ACTUAL_FINNHUB_API_KEY_HERE");

            // Act
            var quote = await _stockService.GetRealTimeQuoteAsync("INVALID_SYMBOL_12345");

            // Assert
            // 對於無效的股票代碼，應該返回 null 或空的報價資料
            // 具體行為取決於 API 的回應
            Assert.True(quote == null || quote.CurrentPrice == 0);
        }

        [Fact]
        public async Task GetStockInfoAsync_WithValidSymbol_ReturnsInfo()
        {
            // Arrange
            await CleanupTestDataAsync();
            await SeedTestApiKeyProviderAsync();
            await CreateTestApiKeyAsync("Finnhub", "TestKey", "YOUR_ACTUAL_FINNHUB_API_KEY_HERE");

            // Act
            var info = await _stockService.GetStockInfoAsync("AAPL");

            // Assert
            if (info != null) // 只有在有有效 API 金鑰時才會有結果
            {
                Assert.NotNull(info.Symbol);
                Assert.NotNull(info.Name);
            }
        }

        [Fact]
        public async Task GetHistoricalPricesAsync_WithValidSymbolAndDateRange_ReturnsData()
        {
            // Arrange
            await CleanupTestDataAsync();
            await SeedTestApiKeyProviderAsync();
            await CreateTestApiKeyAsync("Finnhub", "TestKey", "YOUR_ACTUAL_FINNHUB_API_KEY_HERE");

            var fromDate = DateOnly.FromDateTime(DateTime.UtcNow.AddDays(-30));
            var toDate = DateOnly.FromDateTime(DateTime.UtcNow);

            // Act
            var prices = await _stockService.GetHistoricalPricesAsync("AAPL", fromDate, toDate);

            // Assert
            Assert.NotNull(prices);
            
            if (prices.Any()) // 只有在有有效 API 金鑰時才會有結果
            {
                var firstPrice = prices.First();
                Assert.True(firstPrice.PriceDate >= fromDate);
                Assert.True(firstPrice.PriceDate <= toDate);
                Assert.True(firstPrice.OpenPrice >= 0);
                Assert.True(firstPrice.HighPrice >= 0);
                Assert.True(firstPrice.LowPrice >= 0);
                Assert.True(firstPrice.ClosePrice >= 0);
            }
        }

        [Fact]
        public async Task GetHistoricalPricesAsync_WithInvalidDateRange_ReturnsEmptyData()
        {
            // Arrange
            await CleanupTestDataAsync();
            await SeedTestApiKeyProviderAsync();
            await CreateTestApiKeyAsync("Finnhub", "TestKey", "YOUR_ACTUAL_FINNHUB_API_KEY_HERE");

            var fromDate = DateOnly.FromDateTime(DateTime.UtcNow.AddDays(1)); // 未來日期
            var toDate = DateOnly.FromDateTime(DateTime.UtcNow.AddDays(2));

            // Act
            var prices = await _stockService.GetHistoricalPricesAsync("AAPL", fromDate, toDate);

            // Assert
            Assert.NotNull(prices);
            Assert.Empty(prices);
        }

        [Fact]
        public async Task MultipleApiCalls_WithRateLimit_HandlesGracefully()
        {
            // Arrange
            await CleanupTestDataAsync();
            await SeedTestApiKeyProviderAsync();
            await CreateTestApiKeyAsync("Finnhub", "TestKey", "YOUR_ACTUAL_FINNHUB_API_KEY_HERE");

            var symbols = new[] { "AAPL", "GOOGL", "MSFT", "TSLA", "AMZN" };
            var tasks = new List<Task>();

            // Act
            foreach (var symbol in symbols)
            {
                tasks.Add(_stockService.GetRealTimeQuoteAsync(symbol));
            }

            // 等待所有請求完成，不應該拋出異常
            await Task.WhenAll(tasks);

            // Assert
            // 如果達到速率限制，服務應該優雅地處理而不是崩潰
            Assert.True(true); // 如果到達這裡表示沒有未處理的異常
        }

        [Fact]
        public async Task ApiKeyUsage_IsRecordedCorrectly()
        {
            // Arrange
            await CleanupTestDataAsync();
            await SeedTestApiKeyProviderAsync();
            var testKey = await CreateTestApiKeyAsync("Finnhub", "TestKey", "YOUR_ACTUAL_FINNHUB_API_KEY_HERE");

            // Act
            await _stockService.GetRealTimeQuoteAsync("AAPL");

            // 等待一下讓使用記錄被寫入
            await Task.Delay(100);

            // Assert
            var usageRecords = Context.ApiKeyUsages.Where(u => u.ApiKeyId == testKey.Id).ToList();
            
            // 如果 API 調用成功，應該有使用記錄
            if (usageRecords.Any())
            {
                var record = usageRecords.First();
                Assert.NotNull(record.Endpoint);
                Assert.True(record.ResponseTimeMs >= 0);
                Assert.True(record.RequestTime <= DateTimeOffset.UtcNow);
            }
        }

        [Fact]
        public async Task ConcurrentApiCalls_WithSameKey_HandledCorrectly()
        {
            // Arrange
            await CleanupTestDataAsync();
            await SeedTestApiKeyProviderAsync();
            await CreateTestApiKeyAsync("Finnhub", "TestKey", "YOUR_ACTUAL_FINNHUB_API_KEY_HERE");

            // Act
            var tasks = new[]
            {
                _stockService.GetRealTimeQuoteAsync("AAPL"),
                _stockService.GetRealTimeQuoteAsync("GOOGL"),
                _stockService.GetRealTimeQuoteAsync("MSFT")
            };

            await Task.WhenAll(tasks);

            // Assert
            // 測試通過表示沒有拋出異常
        }
    }
}
