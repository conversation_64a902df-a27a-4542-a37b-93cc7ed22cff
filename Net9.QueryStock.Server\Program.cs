﻿using Microsoft.EntityFrameworkCore;
using Net9.QueryStock.Server.Configuration;
using Net9.QueryStock.Server.Data;
using Net9.QueryStock.Server.Interfaces;
using Net9.QueryStock.Server.Services;

namespace Net9.QueryStock.Server
{
    public class Program
    {
        public static void Main(string[] args)
        {
            var builder = WebApplication.CreateBuilder(args);

            // 資料庫設定
            builder.Services.AddDbContext<Net9QueryStockServerContext>(options =>
                options.UseSqlServer(builder.Configuration.GetConnectionString("Net9QueryStockServerContext") ??
                    throw new InvalidOperationException("Connection string 'Net9QueryStockServerContext' not found.")));

            // 設定選項
            builder.Services.Configure<FinnhubOptions>(
                builder.Configuration.GetSection(FinnhubOptions.SectionName));

            // HTTP 客戶端
            builder.Services.AddHttpClient<FinnhubDataProvider>();

            // 快取服務
            builder.Services.AddMemoryCache();
            builder.Services.AddScoped<ICacheService, MemoryCacheService>();

            // 資料提供者
            builder.Services.AddScoped<IStockDataProvider, FinnhubDataProvider>();

            // 業務服務
            builder.Services.AddScoped<IStockService, StockService>();

            // 控制器
            builder.Services.AddControllers();

            // Add services to the container.
            builder.Services.AddAuthorization();

            // Learn more about configuring OpenAPI at https://aka.ms/aspnet/openapi
            builder.Services.AddOpenApi();

            var app = builder.Build();

            app.UseDefaultFiles();
            app.MapStaticAssets();

            // Configure the HTTP request pipeline.
            if (app.Environment.IsDevelopment())
            {
                app.MapOpenApi();
            }

            app.UseHttpsRedirection();

            app.UseAuthorization();

            // 映射控制器
            app.MapControllers();

            var summaries = new[]
            {
                "Freezing", "Bracing", "Chilly", "Cool", "Mild", "Warm", "Balmy", "Hot", "Sweltering", "Scorching"
            };

            app.MapGet("/weatherforecast", (HttpContext httpContext) =>
            {
                var forecast = Enumerable.Range(1, 5).Select(index =>
                    new WeatherForecast
                    {
                        Date = DateOnly.FromDateTime(DateTime.Now.AddDays(index)),
                        TemperatureC = Random.Shared.Next(-20, 55),
                        Summary = summaries[Random.Shared.Next(summaries.Length)]
                    })
                    .ToArray();
                return forecast;
            })
            .WithName("GetWeatherForecast");

            app.MapFallbackToFile("/index.html");

            app.Run();
        }
    }
}
