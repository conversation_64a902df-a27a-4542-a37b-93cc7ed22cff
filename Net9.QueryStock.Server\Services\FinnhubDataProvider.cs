using Microsoft.Extensions.Options;
using System.Diagnostics;
using System.Text.Json;
using Net9.QueryStock.Server.Configuration;
using Net9.QueryStock.Server.DTOs;
using Net9.QueryStock.Server.Interfaces;
using Net9.QueryStock.Server.Models.Finnhub;

namespace Net9.QueryStock.Server.Services
{
    /// <summary>
    /// Finnhub 資料提供者實作
    /// </summary>
    public class FinnhubDataProvider : IStockDataProvider
    {
        private readonly HttpClient _httpClient;
        private readonly FinnhubOptions _options;
        private readonly IApiKeyPoolService _apiKeyPoolService;
        private readonly ILogger<FinnhubDataProvider> _logger;
        private readonly JsonSerializerOptions _jsonOptions;

        public FinnhubDataProvider(
            HttpClient httpClient,
            IOptions<FinnhubOptions> options,
            IApiKeyPoolService apiKeyPoolService,
            ILogger<FinnhubDataProvider> logger)
        {
            _httpClient = httpClient;
            _options = options.Value;
            _apiKeyPoolService = apiKeyPoolService;
            _logger = logger;

            _jsonOptions = new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true
            };

            ConfigureHttpClient();
        }

        public string ProviderName => "Finnhub";
        public bool IsAvailable => _options.IsEnabled;

        /// <summary>
        /// 設定 HTTP 客戶端
        /// </summary>
        private void ConfigureHttpClient()
        {
            _httpClient.BaseAddress = new Uri(_options.BaseUrl);
            _httpClient.Timeout = TimeSpan.FromSeconds(_options.TimeoutSeconds);
        }

        /// <summary>
        /// 獲取即時股票報價
        /// </summary>
        public async Task<StockQuoteDto?> GetRealTimeQuoteAsync(string symbol, CancellationToken cancellationToken = default)
        {
            var stopwatch = Stopwatch.StartNew();
            string? apiKey = null;

            try
            {
                _logger.LogInformation("正在獲取 {Symbol} 的即時報價", symbol);

                // 從金鑰池獲取可用的 API 金鑰
                apiKey = await _apiKeyPoolService.GetAvailableApiKeyAsync(ProviderName, cancellationToken);
                if (string.IsNullOrEmpty(apiKey))
                {
                    _logger.LogWarning("沒有可用的 API 金鑰用於 Finnhub");
                    return null;
                }

                var url = $"/quote?symbol={symbol}&token={apiKey}";
                var response = await _httpClient.GetAsync(url, cancellationToken);

                stopwatch.Stop();

                if (!response.IsSuccessStatusCode)
                {
                    _logger.LogWarning("Finnhub API 回應失敗: {StatusCode}", response.StatusCode);

                    // 記錄失敗的使用情況
                    await _apiKeyPoolService.RecordUsageAsync(
                        ProviderName, apiKey, "quote", false, (int)stopwatch.ElapsedMilliseconds,
                        $"HTTP {response.StatusCode}", (int)response.StatusCode, cancellationToken);

                    // 如果是認證錯誤，標記金鑰為錯誤狀態
                    if (response.StatusCode == System.Net.HttpStatusCode.Unauthorized ||
                        response.StatusCode == System.Net.HttpStatusCode.Forbidden)
                    {
                        await _apiKeyPoolService.MarkKeyAsErrorAsync(
                            ProviderName, apiKey, $"認證失敗: HTTP {response.StatusCode}", cancellationToken: cancellationToken);
                    }

                    return null;
                }

                var content = await response.Content.ReadAsStringAsync(cancellationToken);
                var finnhubQuote = JsonSerializer.Deserialize<FinnhubQuoteResponse>(content, _jsonOptions);

                if (finnhubQuote == null)
                {
                    _logger.LogWarning("無法解析 Finnhub 報價回應");

                    // 記錄解析失敗
                    await _apiKeyPoolService.RecordUsageAsync(
                        ProviderName, apiKey, "quote", false, (int)stopwatch.ElapsedMilliseconds,
                        "無法解析回應", 200, cancellationToken);

                    return null;
                }

                // 記錄成功的使用情況
                await _apiKeyPoolService.RecordUsageAsync(
                    ProviderName, apiKey, "quote", true, (int)stopwatch.ElapsedMilliseconds,
                    httpStatusCode: 200, cancellationToken: cancellationToken);

                return new StockQuoteDto
                {
                    Symbol = symbol,
                    CurrentPrice = finnhubQuote.CurrentPrice,
                    Change = finnhubQuote.Change,
                    ChangePercent = finnhubQuote.ChangePercent,
                    OpenPrice = finnhubQuote.OpenPrice,
                    HighPrice = finnhubQuote.HighPrice,
                    LowPrice = finnhubQuote.LowPrice,
                    PreviousClose = finnhubQuote.PreviousClose,
                    QuoteTime = DateTimeOffset.FromUnixTimeSeconds(finnhubQuote.Timestamp),
                    DataSource = ProviderName
                };
            }
            catch (Exception ex)
            {
                stopwatch.Stop();
                _logger.LogError(ex, "獲取 {Symbol} 即時報價時發生錯誤", symbol);

                // 記錄異常情況
                if (!string.IsNullOrEmpty(apiKey))
                {
                    await _apiKeyPoolService.RecordUsageAsync(
                        ProviderName, apiKey, "quote", false, (int)stopwatch.ElapsedMilliseconds,
                        ex.Message, cancellationToken: cancellationToken);
                }

                return null;
            }
        }

        /// <summary>
        /// 獲取股票詳細資訊
        /// </summary>
        public async Task<StockInfoDto?> GetStockInfoAsync(string symbol, CancellationToken cancellationToken = default)
        {
            var stopwatch = Stopwatch.StartNew();
            string? apiKey = null;

            try
            {
                _logger.LogInformation("正在獲取 {Symbol} 的詳細資訊", symbol);

                // 從金鑰池獲取可用的 API 金鑰
                apiKey = await _apiKeyPoolService.GetAvailableApiKeyAsync(ProviderName, cancellationToken);
                if (string.IsNullOrEmpty(apiKey))
                {
                    _logger.LogWarning("沒有可用的 API 金鑰用於 Finnhub");
                    return null;
                }

                var url = $"/stock/profile2?symbol={symbol}&token={apiKey}";
                var response = await _httpClient.GetAsync(url, cancellationToken);

                stopwatch.Stop();

                if (!response.IsSuccessStatusCode)
                {
                    _logger.LogWarning("Finnhub API 回應失敗: {StatusCode}", response.StatusCode);

                    await _apiKeyPoolService.RecordUsageAsync(
                        ProviderName, apiKey, "profile", false, (int)stopwatch.ElapsedMilliseconds,
                        $"HTTP {response.StatusCode}", (int)response.StatusCode, cancellationToken);

                    if (response.StatusCode == System.Net.HttpStatusCode.Unauthorized ||
                        response.StatusCode == System.Net.HttpStatusCode.Forbidden)
                    {
                        await _apiKeyPoolService.MarkKeyAsErrorAsync(
                            ProviderName, apiKey, $"認證失敗: HTTP {response.StatusCode}", cancellationToken: cancellationToken);
                    }

                    return null;
                }

                var content = await response.Content.ReadAsStringAsync(cancellationToken);
                var profile = JsonSerializer.Deserialize<FinnhubCompanyProfileResponse>(content, _jsonOptions);

                if (profile == null)
                {
                    _logger.LogWarning("無法解析 Finnhub 公司資料回應");

                    await _apiKeyPoolService.RecordUsageAsync(
                        ProviderName, apiKey, "profile", false, (int)stopwatch.ElapsedMilliseconds,
                        "無法解析回應", 200, cancellationToken);

                    return null;
                }

                await _apiKeyPoolService.RecordUsageAsync(
                    ProviderName, apiKey, "profile", true, (int)stopwatch.ElapsedMilliseconds,
                    httpStatusCode: 200, cancellationToken: cancellationToken);

                return new StockInfoDto
                {
                    Symbol = symbol,
                    Name = profile.Name ?? string.Empty,
                    Exchange = profile.Exchange,
                    Currency = profile.Currency ?? "USD",
                    Industry = profile.Industry,
                    Country = profile.Country,
                    MarketCapitalization = profile.MarketCapitalization,
                    Website = profile.Website,
                    IpoDate = ParseDateOnly(profile.IpoDate)
                };
            }
            catch (Exception ex)
            {
                stopwatch.Stop();
                _logger.LogError(ex, "獲取 {Symbol} 詳細資訊時發生錯誤", symbol);

                if (!string.IsNullOrEmpty(apiKey))
                {
                    await _apiKeyPoolService.RecordUsageAsync(
                        ProviderName, apiKey, "profile", false, (int)stopwatch.ElapsedMilliseconds,
                        ex.Message, cancellationToken: cancellationToken);
                }

                return null;
            }
        }
        
        /// <summary>
        /// 獲取歷史價格資料
        /// </summary>
        public async Task<IEnumerable<StockPriceDto>> GetHistoricalPricesAsync(
            string symbol,
            DateOnly fromDate,
            DateOnly toDate,
            CancellationToken cancellationToken = default)
        {
            try
            {
                _logger.LogInformation("正在獲取 {Symbol} 從 {FromDate} 到 {ToDate} 的歷史價格", symbol, fromDate, toDate);

                var fromTimestamp = ((DateTimeOffset)fromDate.ToDateTime(TimeOnly.MinValue)).ToUnixTimeSeconds();
                var toTimestamp = ((DateTimeOffset)toDate.ToDateTime(TimeOnly.MaxValue)).ToUnixTimeSeconds();

                var apiKey = await _apiKeyPoolService.GetAvailableApiKeyAsync("Finnhub", cancellationToken);
                if (string.IsNullOrEmpty(apiKey))
                {
                    _logger.LogWarning("沒有可用的 Finnhub API 金鑰");
                    return Enumerable.Empty<StockPriceDto>();
                }

                var url = $"/stock/candle?symbol={symbol}&resolution=D&from={fromTimestamp}&to={toTimestamp}&token={apiKey}";
                var response = await _httpClient.GetAsync(url, cancellationToken);

                if (!response.IsSuccessStatusCode)
                {
                    _logger.LogWarning("Finnhub API 回應失敗: {StatusCode}", response.StatusCode);
                    return Enumerable.Empty<StockPriceDto>();
                }

                var content = await response.Content.ReadAsStringAsync(cancellationToken);
                var candleData = JsonSerializer.Deserialize<FinnhubCandleResponse>(content, _jsonOptions);

                if (candleData?.Status != "ok" || candleData.Timestamps == null)
                {
                    _logger.LogWarning("Finnhub 歷史資料回應無效或無資料");
                    return Enumerable.Empty<StockPriceDto>();
                }

                var prices = new List<StockPriceDto>();
                for (int i = 0; i < candleData.Timestamps.Length; i++)
                {
                    var priceDate = DateOnly.FromDateTime(DateTimeOffset.FromUnixTimeSeconds(candleData.Timestamps[i]).DateTime);

                    prices.Add(new StockPriceDto
                    {
                        Symbol = symbol,
                        OpenPrice = candleData.OpenPrices?[i] ?? 0,
                        HighPrice = candleData.HighPrices?[i] ?? 0,
                        LowPrice = candleData.LowPrices?[i] ?? 0,
                        ClosePrice = candleData.ClosePrices?[i] ?? 0,
                        Volume = candleData.Volumes?[i] ?? 0,
                        PriceDate = priceDate,
                        DataSource = ProviderName
                    });
                }

                return prices.OrderBy(p => p.PriceDate);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "獲取 {Symbol} 歷史價格時發生錯誤", symbol);
                return Enumerable.Empty<StockPriceDto>();
            }
        }

        /// <summary>
        /// 搜尋股票
        /// </summary>
        public async Task<IEnumerable<StockInfoDto>> SearchStocksAsync(string query, CancellationToken cancellationToken = default)
        {
            try
            {
                _logger.LogInformation("正在搜尋股票: {Query}", query);

                var apiKey = await _apiKeyPoolService.GetAvailableApiKeyAsync("Finnhub", cancellationToken);
                if (string.IsNullOrEmpty(apiKey))
                {
                    _logger.LogWarning("沒有可用的 Finnhub API 金鑰");
                    return Enumerable.Empty<StockInfoDto>();
                }

                var url = $"/search?q={Uri.EscapeDataString(query)}&token={apiKey}";
                var response = await _httpClient.GetAsync(url, cancellationToken);

                if (!response.IsSuccessStatusCode)
                {
                    _logger.LogWarning("Finnhub API 回應失敗: {StatusCode}", response.StatusCode);
                    return Enumerable.Empty<StockInfoDto>();
                }

                var content = await response.Content.ReadAsStringAsync(cancellationToken);
                var searchResponse = JsonSerializer.Deserialize<FinnhubSearchResponse>(content, _jsonOptions);

                if (searchResponse?.Results == null)
                {
                    _logger.LogWarning("Finnhub 搜尋回應無效");
                    return Enumerable.Empty<StockInfoDto>();
                }

                return searchResponse.Results
                    .Where(r => !string.IsNullOrEmpty(r.Symbol))
                    .Select(r => new StockInfoDto
                    {
                        Symbol = r.Symbol!,
                        Name = r.Description ?? string.Empty,
                        StockType = r.Type
                    });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "搜尋股票時發生錯誤: {Query}", query);
                return Enumerable.Empty<StockInfoDto>();
            }
        }

        /// <summary>
        /// 批量獲取即時報價
        /// </summary>
        public async Task<Dictionary<string, StockQuoteDto>> GetBatchQuotesAsync(
            IEnumerable<string> symbols,
            CancellationToken cancellationToken = default)
        {
            var result = new Dictionary<string, StockQuoteDto>();
            var symbolList = symbols.ToList();

            _logger.LogInformation("正在批量獲取 {Count} 個股票的即時報價", symbolList.Count);

            // Finnhub 不支援批量查詢，所以需要逐一查詢
            var tasks = symbolList.Select(async symbol =>
            {
                var quote = await GetRealTimeQuoteAsync(symbol, cancellationToken);
                return new { Symbol = symbol, Quote = quote };
            });

            var results = await Task.WhenAll(tasks);

            foreach (var item in results)
            {
                if (item.Quote != null)
                {
                    result[item.Symbol] = item.Quote;
                }
            }

            return result;
        }

        /// <summary>
        /// 執行 API 請求的通用方法
        /// </summary>
        private async Task<T?> ExecuteApiRequestAsync<T>(
            string endpoint,
            string operationName,
            CancellationToken cancellationToken = default) where T : class
        {
            var stopwatch = Stopwatch.StartNew();
            string? apiKey = null;

            try
            {
                apiKey = await _apiKeyPoolService.GetAvailableApiKeyAsync(ProviderName, cancellationToken);
                if (string.IsNullOrEmpty(apiKey))
                {
                    _logger.LogWarning("沒有可用的 API 金鑰用於 Finnhub");
                    return null;
                }

                var url = $"{endpoint}&token={apiKey}";
                var response = await _httpClient.GetAsync(url, cancellationToken);

                stopwatch.Stop();

                if (!response.IsSuccessStatusCode)
                {
                    _logger.LogWarning("Finnhub API 回應失敗: {StatusCode}", response.StatusCode);

                    await _apiKeyPoolService.RecordUsageAsync(
                        ProviderName, apiKey, operationName, false, (int)stopwatch.ElapsedMilliseconds,
                        $"HTTP {response.StatusCode}", (int)response.StatusCode, cancellationToken);

                    if (response.StatusCode == System.Net.HttpStatusCode.Unauthorized ||
                        response.StatusCode == System.Net.HttpStatusCode.Forbidden)
                    {
                        await _apiKeyPoolService.MarkKeyAsErrorAsync(
                            ProviderName, apiKey, $"認證失敗: HTTP {response.StatusCode}", cancellationToken: cancellationToken);
                    }

                    return null;
                }

                var content = await response.Content.ReadAsStringAsync(cancellationToken);
                var result = JsonSerializer.Deserialize<T>(content, _jsonOptions);

                if (result == null)
                {
                    _logger.LogWarning("無法解析 Finnhub API 回應");

                    await _apiKeyPoolService.RecordUsageAsync(
                        ProviderName, apiKey, operationName, false, (int)stopwatch.ElapsedMilliseconds,
                        "無法解析回應", 200, cancellationToken);

                    return null;
                }

                await _apiKeyPoolService.RecordUsageAsync(
                    ProviderName, apiKey, operationName, true, (int)stopwatch.ElapsedMilliseconds,
                    httpStatusCode: 200, cancellationToken: cancellationToken);

                return result;
            }
            catch (Exception ex)
            {
                stopwatch.Stop();
                _logger.LogError(ex, "執行 Finnhub API 請求時發生錯誤: {Operation}", operationName);

                if (!string.IsNullOrEmpty(apiKey))
                {
                    await _apiKeyPoolService.RecordUsageAsync(
                        ProviderName, apiKey, operationName, false, (int)stopwatch.ElapsedMilliseconds,
                        ex.Message, cancellationToken: cancellationToken);
                }

                return null;
            }
        }

        /// <summary>
        /// 解析日期字串為 DateOnly
        /// </summary>
        private static DateOnly? ParseDateOnly(string? dateString)
        {
            if (string.IsNullOrEmpty(dateString))
                return null;

            if (DateTime.TryParse(dateString, out var date))
                return DateOnly.FromDateTime(date);

            return null;
        }
    }
}
