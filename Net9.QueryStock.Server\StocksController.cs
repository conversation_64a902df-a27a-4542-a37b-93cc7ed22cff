﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Net9.QueryStock.Server.Data;
using Net9.QueryStock.Server.DTOs;
using Net9.QueryStock.Server.Interfaces;
using Net9.QueryStock.Server.Models;

namespace Net9.QueryStock.Server
{
    /// <summary>
    /// 股票 API 控制器
    /// </summary>
    [Route("api/[controller]")]
    [ApiController]
    public class StocksController : ControllerBase
    {
        private readonly Net9QueryStockServerContext _context;
        private readonly IStockService _stockService;
        private readonly ILogger<StocksController> _logger;

        public StocksController(
            Net9QueryStockServerContext context,
            IStockService stockService,
            ILogger<StocksController> logger)
        {
            _context = context;
            _stockService = stockService;
            _logger = logger;
        }

        /// <summary>
        /// 獲取即時股票報價
        /// </summary>
        /// <param name="symbol">股票代號</param>
        /// <returns>即時報價資訊</returns>
        [HttpGet("quote/{symbol}")]
        public async Task<ActionResult<StockQuoteDto>> GetRealTimeQuote(string symbol)
        {
            try
            {
                _logger.LogInformation("獲取 {Symbol} 的即時報價", symbol);

                var quote = await _stockService.GetRealTimeQuoteAsync(symbol);
                if (quote == null)
                {
                    return NotFound($"找不到股票代號 {symbol} 的報價資訊");
                }

                return Ok(quote);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "獲取 {Symbol} 即時報價時發生錯誤", symbol);
                return StatusCode(500, "獲取股票報價時發生內部錯誤");
            }
        }

        /// <summary>
        /// 獲取股票詳細資訊
        /// </summary>
        /// <param name="symbol">股票代號</param>
        /// <returns>股票詳細資訊</returns>
        [HttpGet("info/{symbol}")]
        public async Task<ActionResult<StockInfoDto>> GetStockInfo(string symbol)
        {
            try
            {
                _logger.LogInformation("獲取 {Symbol} 的詳細資訊", symbol);

                var info = await _stockService.GetStockInfoAsync(symbol);
                if (info == null)
                {
                    return NotFound($"找不到股票代號 {symbol} 的詳細資訊");
                }

                return Ok(info);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "獲取 {Symbol} 詳細資訊時發生錯誤", symbol);
                return StatusCode(500, "獲取股票資訊時發生內部錯誤");
            }
        }

        /// <summary>
        /// 獲取歷史價格資料
        /// </summary>
        /// <param name="symbol">股票代號</param>
        /// <param name="fromDate">開始日期 (yyyy-MM-dd)</param>
        /// <param name="toDate">結束日期 (yyyy-MM-dd)</param>
        /// <returns>歷史價格資料</returns>
        [HttpGet("history/{symbol}")]
        public async Task<ActionResult<IEnumerable<StockPriceDto>>> GetHistoricalPrices(
            string symbol,
            [FromQuery] string fromDate,
            [FromQuery] string toDate)
        {
            try
            {
                if (!DateOnly.TryParse(fromDate, out var from) || !DateOnly.TryParse(toDate, out var to))
                {
                    return BadRequest("日期格式不正確，請使用 yyyy-MM-dd 格式");
                }

                if (from > to)
                {
                    return BadRequest("開始日期不能大於結束日期");
                }

                _logger.LogInformation("獲取 {Symbol} 從 {FromDate} 到 {ToDate} 的歷史價格", symbol, from, to);

                var prices = await _stockService.GetHistoricalPricesAsync(symbol, from, to);
                return Ok(prices);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "獲取 {Symbol} 歷史價格時發生錯誤", symbol);
                return StatusCode(500, "獲取歷史價格時發生內部錯誤");
            }
        }

        /// <summary>
        /// 搜尋股票
        /// </summary>
        /// <param name="query">搜尋關鍵字</param>
        /// <returns>符合條件的股票列表</returns>
        [HttpGet("search")]
        public async Task<ActionResult<IEnumerable<StockInfoDto>>> SearchStocks([FromQuery] string query)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(query))
                {
                    return BadRequest("搜尋關鍵字不能為空");
                }

                _logger.LogInformation("搜尋股票: {Query}", query);

                var results = await _stockService.SearchStocksAsync(query);
                return Ok(results);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "搜尋股票時發生錯誤: {Query}", query);
                return StatusCode(500, "搜尋股票時發生內部錯誤");
            }
        }

        /// <summary>
        /// 批量獲取即時報價
        /// </summary>
        /// <param name="symbols">股票代號列表</param>
        /// <returns>股票報價字典</returns>
        [HttpPost("quotes")]
        public async Task<ActionResult<Dictionary<string, StockQuoteDto>>> GetBatchQuotes([FromBody] string[] symbols)
        {
            try
            {
                if (symbols == null || symbols.Length == 0)
                {
                    return BadRequest("股票代號列表不能為空");
                }

                if (symbols.Length > 50)
                {
                    return BadRequest("一次最多只能查詢 50 個股票");
                }

                _logger.LogInformation("批量獲取 {Count} 個股票的即時報價", symbols.Length);

                var quotes = await _stockService.GetBatchQuotesAsync(symbols);
                return Ok(quotes);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "批量獲取即時報價時發生錯誤");
                return StatusCode(500, "批量獲取報價時發生內部錯誤");
            }
        }

        /// <summary>
        /// 同步股票資料到資料庫
        /// </summary>
        /// <param name="symbol">股票代號</param>
        /// <returns>同步結果</returns>
        [HttpPost("sync/{symbol}")]
        public async Task<ActionResult> SyncStockData(string symbol)
        {
            try
            {
                _logger.LogInformation("開始同步 {Symbol} 的股票資料", symbol);

                var success = await _stockService.SyncStockDataAsync(symbol);
                if (success)
                {
                    return Ok(new { message = $"成功同步股票 {symbol} 的資料" });
                }
                else
                {
                    return BadRequest(new { message = $"同步股票 {symbol} 的資料失敗" });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "同步 {Symbol} 股票資料時發生錯誤", symbol);
                return StatusCode(500, "同步股票資料時發生內部錯誤");
            }
        }

        /// <summary>
        /// 獲取資料庫中的所有股票（舊版 API，建議使用 /stored）
        /// </summary>
        /// <returns>股票列表</returns>
        [HttpGet]
        public async Task<ActionResult<IEnumerable<Stock>>> GetStock()
        {
            return await _context.Stock.ToListAsync();
        }

        /// <summary>
        /// 獲取資料庫中儲存的股票列表
        /// </summary>
        /// <returns>股票列表</returns>
        [HttpGet("stored")]
        public async Task<ActionResult<IEnumerable<Stock>>> GetStoredStocks()
        {
            try
            {
                var stocks = await _stockService.GetStoredStocksAsync();
                return Ok(stocks);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "獲取儲存的股票列表時發生錯誤");
                return StatusCode(500, "獲取股票列表時發生內部錯誤");
            }
        }

        /// <summary>
        /// 根據ID獲取股票詳細資訊
        /// </summary>
        /// <param name="id">股票ID</param>
        /// <returns>股票詳細資訊</returns>
        [HttpGet("{id}")]
        public async Task<ActionResult<Stock>> GetStock(Guid id)
        {
            try
            {
                var stock = await _stockService.GetStockByIdAsync(id);
                if (stock == null)
                {
                    return NotFound($"找不到ID為 {id} 的股票");
                }

                return Ok(stock);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "獲取股票 {Id} 時發生錯誤", id);
                return StatusCode(500, "獲取股票資訊時發生內部錯誤");
            }
        }

        /// <summary>
        /// 根據股票代號獲取股票詳細資訊
        /// </summary>
        /// <param name="symbol">股票代號</param>
        /// <returns>股票詳細資訊</returns>
        [HttpGet("symbol/{symbol}")]
        public async Task<ActionResult<Stock>> GetStockBySymbol(string symbol)
        {
            try
            {
                var stock = await _stockService.GetStockBySymbolAsync(symbol);
                if (stock == null)
                {
                    return NotFound($"找不到股票代號 {symbol}");
                }

                return Ok(stock);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "獲取股票 {Symbol} 時發生錯誤", symbol);
                return StatusCode(500, "獲取股票資訊時發生內部錯誤");
            }
        }

        // PUT: api/Stocks/5
        // To protect from overposting attacks, see https://go.microsoft.com/fwlink/?linkid=2123754
        [HttpPut("{id}")]
        public async Task<IActionResult> PutStock(Guid id, Stock stock)
        {
            if (id != stock.Id)
            {
                return BadRequest();
            }

            _context.Entry(stock).State = EntityState.Modified;

            try
            {
                await _context.SaveChangesAsync();
            }
            catch (DbUpdateConcurrencyException)
            {
                if (!StockExists(id))
                {
                    return NotFound();
                }
                else
                {
                    throw;
                }
            }

            return NoContent();
        }

        // POST: api/Stocks
        // To protect from overposting attacks, see https://go.microsoft.com/fwlink/?linkid=2123754
        [HttpPost]
        public async Task<ActionResult<Stock>> PostStock(Stock stock)
        {
            _context.Stock.Add(stock);
            await _context.SaveChangesAsync();

            return CreatedAtAction("GetStock", new { id = stock.Id }, stock);
        }

        // DELETE: api/Stocks/5
        [HttpDelete("{id}")]
        public async Task<IActionResult> DeleteStock(Guid id)
        {
            var stock = await _context.Stock.FindAsync(id);
            if (stock == null)
            {
                return NotFound();
            }

            _context.Stock.Remove(stock);
            await _context.SaveChangesAsync();

            return NoContent();
        }

        private bool StockExists(Guid id)
        {
            return _context.Stock.Any(e => e.Id == id);
        }
    }
}
