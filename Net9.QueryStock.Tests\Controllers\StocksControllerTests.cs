using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Moq;
using Net9.QueryStock.Server;
using Net9.QueryStock.Server.Data;
using Net9.QueryStock.Server.DTOs;
using Net9.QueryStock.Server.Interfaces;
using Net9.QueryStock.Server.Models;

namespace Net9.QueryStock.Tests.Controllers
{
    /// <summary>
    /// StocksController 單元測試
    /// </summary>
    public class StocksControllerTests
    {
        private readonly Mock<IStockService> _mockStockService;
        private readonly Mock<ILogger<StocksController>> _mockLogger;
        private readonly Net9QueryStockServerContext _dbContext;

        public StocksControllerTests()
        {
            _mockStockService = new Mock<IStockService>();
            _mockLogger = new Mock<ILogger<StocksController>>();
            
            // 設定記憶體資料庫
            var options = new DbContextOptionsBuilder<Net9QueryStockServerContext>()
                .UseInMemoryDatabase(databaseName: Guid.NewGuid().ToString())
                .Options;
                
            _dbContext = new Net9QueryStockServerContext(options);
        }

        [Fact]
        public async Task GetRealTimeQuote_WhenQuoteExists_ShouldReturnOk()
        {
            // Arrange
            var symbol = "AAPL";
            var quote = new StockQuoteDto
            {
                Symbol = symbol,
                CurrentPrice = 150.25m,
                Change = 2.50m,
                ChangePercent = 1.69m
            };
            
            _mockStockService
                .Setup(s => s.GetRealTimeQuoteAsync(symbol, It.IsAny<CancellationToken>()))
                .ReturnsAsync(quote);
                
            var controller = CreateController();

            // Act
            var result = await controller.GetRealTimeQuote(symbol);

            // Assert
            var okResult = Assert.IsType<OkObjectResult>(result.Result);
            var returnedQuote = Assert.IsType<StockQuoteDto>(okResult.Value);
            Assert.Equal(quote.Symbol, returnedQuote.Symbol);
            Assert.Equal(quote.CurrentPrice, returnedQuote.CurrentPrice);
        }

        [Fact]
        public async Task GetRealTimeQuote_WhenQuoteNotFound_ShouldReturnNotFound()
        {
            // Arrange
            var symbol = "INVALID";
            
            _mockStockService
                .Setup(s => s.GetRealTimeQuoteAsync(symbol, It.IsAny<CancellationToken>()))
                .ReturnsAsync((StockQuoteDto?)null);
                
            var controller = CreateController();

            // Act
            var result = await controller.GetRealTimeQuote(symbol);

            // Assert
            var notFoundResult = Assert.IsType<NotFoundObjectResult>(result.Result);
            Assert.Contains(symbol, notFoundResult.Value?.ToString());
        }

        [Fact]
        public async Task GetStockInfo_WhenInfoExists_ShouldReturnOk()
        {
            // Arrange
            var symbol = "AAPL";
            var info = new StockInfoDto
            {
                Symbol = symbol,
                Name = "Apple Inc",
                Exchange = "NASDAQ",
                Currency = "USD"
            };
            
            _mockStockService
                .Setup(s => s.GetStockInfoAsync(symbol, It.IsAny<CancellationToken>()))
                .ReturnsAsync(info);
                
            var controller = CreateController();

            // Act
            var result = await controller.GetStockInfo(symbol);

            // Assert
            var okResult = Assert.IsType<OkObjectResult>(result.Result);
            var returnedInfo = Assert.IsType<StockInfoDto>(okResult.Value);
            Assert.Equal(info.Symbol, returnedInfo.Symbol);
            Assert.Equal(info.Name, returnedInfo.Name);
        }

        [Fact]
        public async Task GetHistoricalPrices_WithValidDates_ShouldReturnOk()
        {
            // Arrange
            var symbol = "AAPL";
            var fromDate = "2023-01-01";
            var toDate = "2023-01-31";
            var prices = new List<StockPriceDto>
            {
                new StockPriceDto { Symbol = symbol, ClosePrice = 150.25m, PriceDate = new DateOnly(2023, 1, 1) },
                new StockPriceDto { Symbol = symbol, ClosePrice = 151.50m, PriceDate = new DateOnly(2023, 1, 2) }
            };
            
            _mockStockService
                .Setup(s => s.GetHistoricalPricesAsync(symbol, It.IsAny<DateOnly>(), It.IsAny<DateOnly>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(prices);
                
            var controller = CreateController();

            // Act
            var result = await controller.GetHistoricalPrices(symbol, fromDate, toDate);

            // Assert
            var okResult = Assert.IsType<OkObjectResult>(result.Result);
            var returnedPrices = Assert.IsAssignableFrom<IEnumerable<StockPriceDto>>(okResult.Value);
            Assert.Equal(2, returnedPrices.Count());
        }

        [Fact]
        public async Task GetHistoricalPrices_WithInvalidDates_ShouldReturnBadRequest()
        {
            // Arrange
            var symbol = "AAPL";
            var fromDate = "invalid-date";
            var toDate = "2023-01-31";
            
            var controller = CreateController();

            // Act
            var result = await controller.GetHistoricalPrices(symbol, fromDate, toDate);

            // Assert
            var badRequestResult = Assert.IsType<BadRequestObjectResult>(result.Result);
            Assert.Contains("日期格式不正確", badRequestResult.Value?.ToString());
        }

        [Fact]
        public async Task SearchStocks_WithValidQuery_ShouldReturnOk()
        {
            // Arrange
            var query = "Apple";
            var searchResults = new List<StockInfoDto>
            {
                new StockInfoDto { Symbol = "AAPL", Name = "Apple Inc" },
                new StockInfoDto { Symbol = "AAPL.L", Name = "Apple Inc - London" }
            };
            
            _mockStockService
                .Setup(s => s.SearchStocksAsync(query, It.IsAny<CancellationToken>()))
                .ReturnsAsync(searchResults);
                
            var controller = CreateController();

            // Act
            var result = await controller.SearchStocks(query);

            // Assert
            var okResult = Assert.IsType<OkObjectResult>(result.Result);
            var returnedResults = Assert.IsAssignableFrom<IEnumerable<StockInfoDto>>(okResult.Value);
            Assert.Equal(2, returnedResults.Count());
        }

        [Fact]
        public async Task SearchStocks_WithEmptyQuery_ShouldReturnBadRequest()
        {
            // Arrange
            var query = "";
            var controller = CreateController();

            // Act
            var result = await controller.SearchStocks(query);

            // Assert
            var badRequestResult = Assert.IsType<BadRequestObjectResult>(result.Result);
            Assert.Contains("搜尋關鍵字不能為空", badRequestResult.Value?.ToString());
        }

        [Fact]
        public async Task GetBatchQuotes_WithValidSymbols_ShouldReturnOk()
        {
            // Arrange
            var symbols = new[] { "AAPL", "MSFT", "GOOG" };
            var quotes = new Dictionary<string, StockQuoteDto>
            {
                { "AAPL", new StockQuoteDto { Symbol = "AAPL", CurrentPrice = 150.25m } },
                { "MSFT", new StockQuoteDto { Symbol = "MSFT", CurrentPrice = 300.50m } }
            };
            
            _mockStockService
                .Setup(s => s.GetBatchQuotesAsync(symbols, It.IsAny<CancellationToken>()))
                .ReturnsAsync(quotes);
                
            var controller = CreateController();

            // Act
            var result = await controller.GetBatchQuotes(symbols);

            // Assert
            var okResult = Assert.IsType<OkObjectResult>(result.Result);
            var returnedQuotes = Assert.IsType<Dictionary<string, StockQuoteDto>>(okResult.Value);
            Assert.Equal(2, returnedQuotes.Count);
            Assert.Contains("AAPL", returnedQuotes.Keys);
            Assert.Contains("MSFT", returnedQuotes.Keys);
        }

        [Fact]
        public async Task GetBatchQuotes_WithTooManySymbols_ShouldReturnBadRequest()
        {
            // Arrange
            var symbols = Enumerable.Range(1, 51).Select(i => $"STOCK{i}").ToArray();
            var controller = CreateController();

            // Act
            var result = await controller.GetBatchQuotes(symbols);

            // Assert
            var badRequestResult = Assert.IsType<BadRequestObjectResult>(result.Result);
            Assert.Contains("一次最多只能查詢 50 個股票", badRequestResult.Value?.ToString());
        }

        [Fact]
        public async Task SyncStockData_WhenSuccessful_ShouldReturnOk()
        {
            // Arrange
            var symbol = "AAPL";
            
            _mockStockService
                .Setup(s => s.SyncStockDataAsync(symbol, It.IsAny<CancellationToken>()))
                .ReturnsAsync(true);
                
            var controller = CreateController();

            // Act
            var result = await controller.SyncStockData(symbol);

            // Assert
            var okResult = Assert.IsType<OkObjectResult>(result);
            Assert.Contains("成功同步", okResult.Value?.ToString());
        }

        private StocksController CreateController()
        {
            return new StocksController(_dbContext, _mockStockService.Object, _mockLogger.Object);
        }
    }
}
