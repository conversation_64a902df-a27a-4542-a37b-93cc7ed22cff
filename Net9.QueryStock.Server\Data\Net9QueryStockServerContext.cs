﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using Net9.QueryStock.Server.Models;

namespace Net9.QueryStock.Server.Data
{
    public class Net9QueryStockServerContext : DbContext
    {
        public Net9QueryStockServerContext (DbContextOptions<Net9QueryStockServerContext> options)
            : base(options)
        {
        }

        public DbSet<Net9.QueryStock.Server.Models.Stock> Stock { get; set; } = default!;
    }
}
