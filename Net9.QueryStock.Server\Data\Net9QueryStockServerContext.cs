﻿using Microsoft.EntityFrameworkCore;
using Net9.QueryStock.Server.Models;

namespace Net9.QueryStock.Server.Data
{
    /// <summary>
    /// 股票查詢系統資料庫上下文
    /// </summary>
    public class Net9QueryStockServerContext : DbContext
    {
        public Net9QueryStockServerContext(DbContextOptions<Net9QueryStockServerContext> options)
            : base(options)
        {
        }

        /// <summary>
        /// 股票基本資訊
        /// </summary>
        public DbSet<Stock> Stock { get; set; } = default!;

        /// <summary>
        /// 股票價格歷史記錄
        /// </summary>
        public DbSet<StockPrice> StockPrices { get; set; } = default!;

        /// <summary>
        /// 股票詳細資訊
        /// </summary>
        public DbSet<StockInfo> StockInfos { get; set; } = default!;

        /// <summary>
        /// 即時股價報價
        /// </summary>
        public DbSet<RealTimeQuote> RealTimeQuotes { get; set; } = default!;

        /// <summary>
        /// 配置資料模型
        /// </summary>
        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);

            // 配置 Stock 實體
            modelBuilder.Entity<Stock>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.HasIndex(e => e.Symbol).IsUnique();
                entity.Property(e => e.Symbol).IsRequired().HasMaxLength(20);
                entity.Property(e => e.Name).IsRequired().HasMaxLength(200);
                entity.Property(e => e.Exchange).HasMaxLength(50);
                entity.Property(e => e.Currency).HasMaxLength(10).HasDefaultValue("USD");
                entity.Property(e => e.StockType).HasMaxLength(50);
                entity.Property(e => e.Industry).HasMaxLength(100);
                entity.Property(e => e.Country).HasMaxLength(50);
                entity.Property(e => e.IsActive).HasDefaultValue(true);
                entity.Property(e => e.CreatedAt).HasDefaultValueSql("GETUTCDATE()");
                entity.Property(e => e.UpdatedAt).HasDefaultValueSql("GETUTCDATE()");
            });

            // 配置 StockPrice 實體
            modelBuilder.Entity<StockPrice>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.HasIndex(e => new { e.StockId, e.PriceDate }).IsUnique();
                entity.Property(e => e.OpenPrice).HasColumnType("decimal(18,4)");
                entity.Property(e => e.HighPrice).HasColumnType("decimal(18,4)");
                entity.Property(e => e.LowPrice).HasColumnType("decimal(18,4)");
                entity.Property(e => e.ClosePrice).HasColumnType("decimal(18,4)");
                entity.Property(e => e.AdjustedClosePrice).HasColumnType("decimal(18,4)");
                entity.Property(e => e.DataSource).IsRequired().HasMaxLength(50);
                entity.Property(e => e.CreatedAt).HasDefaultValueSql("GETUTCDATE()");

                // 設定外鍵關係
                entity.HasOne(e => e.Stock)
                    .WithMany(s => s.StockPrices)
                    .HasForeignKey(e => e.StockId)
                    .OnDelete(DeleteBehavior.Cascade);
            });

            // 配置 StockInfo 實體
            modelBuilder.Entity<StockInfo>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.HasIndex(e => e.StockId).IsUnique();
                entity.Property(e => e.MarketCapitalization).HasColumnType("decimal(18,2)");
                entity.Property(e => e.PeRatio).HasColumnType("decimal(10,2)");
                entity.Property(e => e.PbRatio).HasColumnType("decimal(10,2)");
                entity.Property(e => e.DividendYield).HasColumnType("decimal(5,4)");
                entity.Property(e => e.EarningsPerShare).HasColumnType("decimal(10,4)");
                entity.Property(e => e.Week52High).HasColumnType("decimal(18,4)");
                entity.Property(e => e.Week52Low).HasColumnType("decimal(18,4)");
                entity.Property(e => e.Description).HasMaxLength(2000);
                entity.Property(e => e.Website).HasMaxLength(500);
                entity.Property(e => e.Headquarters).HasMaxLength(500);
                entity.Property(e => e.UpdatedAt).HasDefaultValueSql("GETUTCDATE()");

                // 設定外鍵關係
                entity.HasOne(e => e.Stock)
                    .WithOne(s => s.StockInfo)
                    .HasForeignKey<StockInfo>(e => e.StockId)
                    .OnDelete(DeleteBehavior.Cascade);
            });

            // 配置 RealTimeQuote 實體
            modelBuilder.Entity<RealTimeQuote>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.HasIndex(e => e.Symbol);
                entity.HasIndex(e => e.QuoteTime);
                entity.Property(e => e.Symbol).IsRequired().HasMaxLength(20);
                entity.Property(e => e.CurrentPrice).HasColumnType("decimal(18,4)");
                entity.Property(e => e.Change).HasColumnType("decimal(18,4)");
                entity.Property(e => e.ChangePercent).HasColumnType("decimal(8,4)");
                entity.Property(e => e.OpenPrice).HasColumnType("decimal(18,4)");
                entity.Property(e => e.HighPrice).HasColumnType("decimal(18,4)");
                entity.Property(e => e.LowPrice).HasColumnType("decimal(18,4)");
                entity.Property(e => e.PreviousClose).HasColumnType("decimal(18,4)");
                entity.Property(e => e.BidPrice).HasColumnType("decimal(18,4)");
                entity.Property(e => e.AskPrice).HasColumnType("decimal(18,4)");
                entity.Property(e => e.MarketStatus).HasMaxLength(20);
                entity.Property(e => e.DataSource).IsRequired().HasMaxLength(50);
                entity.Property(e => e.CreatedAt).HasDefaultValueSql("GETUTCDATE()");
            });
        }
    }
}
