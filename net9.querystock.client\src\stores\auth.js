import { defineStore } from 'pinia'
import axios from 'axios'

export const useAuthStore = defineStore('auth', {
  state: () => ({
    user: null,
    token: localStorage.getItem('token'),
    isAuthenticated: false,
    loading: false,
    error: null
  }),

  getters: {
    isLoggedIn: (state) => !!state.token && !!state.user,
    userRole: (state) => state.user?.role || 'Guest',
    userName: (state) => state.user?.username || ''
  },

  actions: {
    // 初始化認證狀態
    async initAuth() {
      const token = localStorage.getItem('token')
      if (token) {
        this.token = token
        this.setAuthHeader(token)
        try {
          await this.getCurrentUser()
        } catch (error) {
          console.error('初始化認證失敗:', error)
          this.logout()
        }
      }
    },

    // 設定 HTTP 請求標頭
    setAuthHeader(token) {
      if (token) {
        axios.defaults.headers.common['Authorization'] = `Bearer ${token}`
      } else {
        delete axios.defaults.headers.common['Authorization']
      }
    },

    // 用戶註冊
    async register(userData) {
      this.loading = true
      this.error = null
      
      try {
        const response = await axios.post('/api/auth/register', userData)
        
        if (response.data.success) {
          this.token = response.data.token
          this.user = response.data.user
          this.isAuthenticated = true
          
          // 儲存到本地存儲
          localStorage.setItem('token', this.token)
          this.setAuthHeader(this.token)
          
          return { success: true, message: response.data.message }
        } else {
          this.error = response.data.message
          return { success: false, message: response.data.message }
        }
      } catch (error) {
        const message = error.response?.data?.message || '註冊失敗，請稍後再試'
        this.error = message
        return { success: false, message }
      } finally {
        this.loading = false
      }
    },

    // 用戶登入
    async login(credentials) {
      this.loading = true
      this.error = null
      
      try {
        const response = await axios.post('/api/auth/login', credentials)
        
        if (response.data.success) {
          this.token = response.data.token
          this.user = response.data.user
          this.isAuthenticated = true
          
          // 儲存到本地存儲
          localStorage.setItem('token', this.token)
          this.setAuthHeader(this.token)
          
          return { success: true, message: response.data.message }
        } else {
          this.error = response.data.message
          return { success: false, message: response.data.message }
        }
      } catch (error) {
        const message = error.response?.data?.message || '登入失敗，請稍後再試'
        this.error = message
        return { success: false, message }
      } finally {
        this.loading = false
      }
    },

    // 獲取當前用戶資訊
    async getCurrentUser() {
      try {
        const response = await axios.get('/api/auth/me')
        this.user = response.data
        this.isAuthenticated = true
        return response.data
      } catch (error) {
        console.error('獲取用戶資訊失敗:', error)
        throw error
      }
    },

    // 修改密碼
    async changePassword(passwordData) {
      this.loading = true
      this.error = null
      
      try {
        const response = await axios.post('/api/auth/change-password', passwordData)
        
        if (response.data.success) {
          return { success: true, message: response.data.message }
        } else {
          this.error = response.data.message
          return { success: false, message: response.data.message }
        }
      } catch (error) {
        const message = error.response?.data?.message || '修改密碼失敗，請稍後再試'
        this.error = message
        return { success: false, message }
      } finally {
        this.loading = false
      }
    },

    // 用戶登出
    logout() {
      this.user = null
      this.token = null
      this.isAuthenticated = false
      this.error = null
      
      // 清除本地存儲
      localStorage.removeItem('token')
      this.setAuthHeader(null)
    },

    // 清除錯誤
    clearError() {
      this.error = null
    }
  }
})

// 設定 axios 攔截器處理認證錯誤
axios.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      const authStore = useAuthStore()
      authStore.logout()
      // 可以在這裡添加重定向到登入頁面的邏輯
    }
    return Promise.reject(error)
  }
)
