using System.ComponentModel.DataAnnotations;

namespace Net9.QueryStock.Server.DTOs
{
    /// <summary>
    /// 用戶註冊請求
    /// </summary>
    public class RegisterRequest
    {
        /// <summary>
        /// 用戶名稱
        /// </summary>
        [Required(ErrorMessage = "用戶名稱不能為空")]
        [StringLength(50, MinimumLength = 3, ErrorMessage = "用戶名稱長度必須在 3-50 字元之間")]
        public string Username { get; set; } = string.Empty;
        
        /// <summary>
        /// 密碼
        /// </summary>
        [Required(ErrorMessage = "密碼不能為空")]
        [StringLength(100, MinimumLength = 6, ErrorMessage = "密碼長度必須在 6-100 字元之間")]
        public string Password { get; set; } = string.Empty;
        
        /// <summary>
        /// 確認密碼
        /// </summary>
        [Required(ErrorMessage = "確認密碼不能為空")]
        [Compare("Password", ErrorMessage = "密碼和確認密碼不一致")]
        public string ConfirmPassword { get; set; } = string.Empty;
        
        /// <summary>
        /// 電子郵件（可選）
        /// </summary>
        [EmailAddress(ErrorMessage = "電子郵件格式不正確")]
        public string? Email { get; set; }
    }

    /// <summary>
    /// 用戶登入請求
    /// </summary>
    public class LoginRequest
    {
        /// <summary>
        /// 用戶名稱
        /// </summary>
        [Required(ErrorMessage = "用戶名稱不能為空")]
        public string Username { get; set; } = string.Empty;
        
        /// <summary>
        /// 密碼
        /// </summary>
        [Required(ErrorMessage = "密碼不能為空")]
        public string Password { get; set; } = string.Empty;
    }

    /// <summary>
    /// 認證回應
    /// </summary>
    public class AuthResponse
    {
        /// <summary>
        /// 是否成功
        /// </summary>
        public bool Success { get; set; }
        
        /// <summary>
        /// 訊息
        /// </summary>
        public string Message { get; set; } = string.Empty;
        
        /// <summary>
        /// JWT 權杖
        /// </summary>
        public string? Token { get; set; }
        
        /// <summary>
        /// 權杖過期時間
        /// </summary>
        public DateTimeOffset? ExpiresAt { get; set; }
        
        /// <summary>
        /// 用戶資訊
        /// </summary>
        public UserDto? User { get; set; }
    }

    /// <summary>
    /// 用戶資訊 DTO
    /// </summary>
    public class UserDto
    {
        /// <summary>
        /// 用戶 ID
        /// </summary>
        public Guid Id { get; set; }
        
        /// <summary>
        /// 用戶名稱
        /// </summary>
        public string Username { get; set; } = string.Empty;
        
        /// <summary>
        /// 電子郵件
        /// </summary>
        public string? Email { get; set; }
        
        /// <summary>
        /// 用戶角色
        /// </summary>
        public string Role { get; set; } = string.Empty;
        
        /// <summary>
        /// 是否啟用
        /// </summary>
        public bool IsActive { get; set; }
        
        /// <summary>
        /// 最後登入時間
        /// </summary>
        public DateTimeOffset? LastLoginAt { get; set; }
        
        /// <summary>
        /// 建立時間
        /// </summary>
        public DateTimeOffset CreatedAt { get; set; }
    }

    /// <summary>
    /// 修改密碼請求
    /// </summary>
    public class ChangePasswordRequest
    {
        /// <summary>
        /// 當前密碼
        /// </summary>
        [Required(ErrorMessage = "當前密碼不能為空")]
        public string CurrentPassword { get; set; } = string.Empty;
        
        /// <summary>
        /// 新密碼
        /// </summary>
        [Required(ErrorMessage = "新密碼不能為空")]
        [StringLength(100, MinimumLength = 6, ErrorMessage = "新密碼長度必須在 6-100 字元之間")]
        public string NewPassword { get; set; } = string.Empty;
        
        /// <summary>
        /// 確認新密碼
        /// </summary>
        [Required(ErrorMessage = "確認新密碼不能為空")]
        [Compare("NewPassword", ErrorMessage = "新密碼和確認密碼不一致")]
        public string ConfirmNewPassword { get; set; } = string.Empty;
    }
}
