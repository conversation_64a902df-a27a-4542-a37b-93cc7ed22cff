# 股票查詢平台測試文件

## 概述

本測試套件為股票查詢平台提供全面的測試覆蓋，包括單元測試、整合測試和端到端測試。所有測試都使用實際的資料庫和外部服務進行驗證。

## 測試結構

```
Tests/
├── TestBase.cs                           # 測試基底類別
├── Services/                             # 服務層測試
│   ├── ApiKeyPoolServiceTests.cs         # API 金鑰池服務測試
│   ├── AuthServiceTests.cs               # 認證服務測試
│   └── StockServiceTests.cs              # 股票服務測試
├── Controllers/                          # 控制器測試
│   ├── AuthControllerTests.cs            # 認證控制器測試
│   └── ApiKeyManagementControllerTests.cs # 金鑰管理控制器測試
├── Integration/                          # 整合測試
│   ├── FinnhubIntegrationTests.cs        # Finnhub API 整合測試
│   └── DatabaseIntegrationTests.cs       # 資料庫整合測試
├── appsettings.Testing.json              # 測試配置
└── README.md                             # 本文件
```

## 測試類型

### 1. 單元測試 (Unit Tests)
- **ApiKeyPoolServiceTests**: 測試 API 金鑰池的管理功能
- **AuthServiceTests**: 測試用戶認證、註冊、JWT 權杖生成
- **StockServiceTests**: 測試股票資料查詢和處理

### 2. 整合測試 (Integration Tests)
- **FinnhubIntegrationTests**: 測試與 Finnhub API 的實際整合
- **DatabaseIntegrationTests**: 測試資料庫操作、約束和效能

### 3. 控制器測試 (Controller Tests)
- **AuthControllerTests**: 測試認證相關的 HTTP 端點
- **ApiKeyManagementControllerTests**: 測試金鑰管理的 HTTP 端點

## 執行測試

### 前置條件

1. **.NET 9 SDK**: 確保已安裝 .NET 9
2. **SQL Server LocalDB**: 用於測試資料庫
3. **Finnhub API 金鑰** (可選): 用於外部 API 測試

### 執行方式

#### 使用 PowerShell 腳本 (推薦)
```powershell
.\run-tests.ps1
```

#### 使用 .NET CLI
```bash
# 執行所有測試
dotnet test

# 執行特定測試類別
dotnet test --filter "ClassName=AuthServiceTests"

# 執行特定測試方法
dotnet test --filter "MethodName=RegisterAsync_WithValidData_ReturnsSuccessAndJwtToken"

# 產生測試報告
dotnet test --logger "trx;LogFileName=TestResults.trx"
```

## 測試配置

### 資料庫設定

測試使用獨立的測試資料庫：
- **連接字串**: `Server=(localdb)\\mssqllocaldb;Database=Net9QueryStockTest;Trusted_Connection=true`
- **自動清理**: 每個測試後自動清理測試資料
- **種子資料**: 自動建立必要的基礎資料

### 外部 API 測試

#### 啟用實際 API 測試
1. 在 `appsettings.Testing.json` 中設定：
   ```json
   {
     "TestSettings": {
       "UseRealApiKeys": true,
       "FinnhubTestApiKey": "YOUR_ACTUAL_API_KEY",
       "SkipExternalApiTests": false
     }
   }
   ```

2. 或在 `FinnhubIntegrationTests.cs` 中直接替換：
   ```csharp
   private const string TEST_API_KEY = "YOUR_ACTUAL_FINNHUB_API_KEY_HERE";
   ```

#### 跳過外部 API 測試
如果沒有 API 金鑰，相關測試會自動跳過，不會影響其他測試的執行。

## 測試覆蓋範圍

### 功能測試
- ✅ 用戶註冊和登入
- ✅ JWT 權杖生成和驗證
- ✅ 密碼雜湊和驗證
- ✅ API 金鑰加密和解密
- ✅ 金鑰池管理和輪換
- ✅ 股票資料查詢
- ✅ 使用記錄追蹤

### 資料庫測試
- ✅ 表格結構和約束
- ✅ 外鍵關聯和級聯刪除
- ✅ 唯一性約束
- ✅ 交易處理
- ✅ 並發存取
- ✅ 查詢效能

### 外部服務測試
- ✅ Finnhub API 整合
- ✅ 股票搜尋功能
- ✅ 即時報價查詢
- ✅ 歷史價格資料
- ✅ 公司資訊查詢
- ✅ 錯誤處理和重試
- ✅ 速率限制處理

### HTTP 端點測試
- ✅ 認證端點 (`/api/auth/*`)
- ✅ 金鑰管理端點 (`/api/apikeymanagement/*`)
- ✅ 股票查詢端點 (`/api/stock/*`)
- ✅ 請求驗證和錯誤處理
- ✅ 授權和權限檢查

## 測試最佳實踐

### 1. 測試隔離
- 每個測試都是獨立的，不依賴其他測試的結果
- 使用 `CleanupTestDataAsync()` 確保測試資料清理
- 使用獨立的測試資料庫避免影響開發資料

### 2. 實際環境測試
- 使用真實的 SQL Server 資料庫而非記憶體資料庫
- 測試實際的外部 API 調用
- 驗證完整的 HTTP 請求流程

### 3. 錯誤情境測試
- 測試無效輸入的處理
- 測試網路錯誤和超時
- 測試資料庫約束違反
- 測試並發存取情境

### 4. 效能測試
- 驗證查詢效能在可接受範圍內
- 測試大量資料的處理能力
- 驗證並發請求的處理

## 故障排除

### 常見問題

1. **資料庫連接失敗**
   ```
   解決方案：確保 SQL Server LocalDB 已安裝並運行
   ```

2. **外部 API 測試失敗**
   ```
   解決方案：檢查 API 金鑰是否有效，或設定跳過外部測試
   ```

3. **權限錯誤**
   ```
   解決方案：確保有足夠權限建立和刪除測試資料庫
   ```

### 除錯技巧

1. **啟用詳細日誌**
   ```bash
   dotnet test --verbosity diagnostic
   ```

2. **執行特定測試**
   ```bash
   dotnet test --filter "FullyQualifiedName~AuthServiceTests"
   ```

3. **檢查測試輸出**
   ```bash
   dotnet test --logger "console;verbosity=detailed"
   ```

## 持續整合

測試套件設計為可在 CI/CD 管道中執行：

```yaml
# GitHub Actions 範例
- name: Run Tests
  run: |
    dotnet test --no-build --verbosity normal --logger trx --results-directory TestResults
    
- name: Publish Test Results
  uses: dorny/test-reporter@v1
  if: success() || failure()
  with:
    name: Test Results
    path: TestResults/*.trx
    reporter: dotnet-trx
```

## 貢獻指南

### 新增測試

1. 繼承 `TestBase` 類別
2. 使用 `CleanupTestDataAsync()` 清理資料
3. 遵循 AAA 模式 (Arrange, Act, Assert)
4. 提供清晰的測試名稱和註解

### 測試命名規範

```csharp
[Fact]
public async Task MethodName_WithCondition_ExpectedResult()
{
    // 測試實作
}
```

### 範例測試

```csharp
[Fact]
public async Task RegisterAsync_WithValidData_ReturnsSuccessAndJwtToken()
{
    // Arrange
    await CleanupTestDataAsync();
    var request = new RegisterRequest { /* ... */ };

    // Act
    var result = await _authService.RegisterAsync(request);

    // Assert
    Assert.True(result.Success);
    Assert.NotNull(result.Token);
}
```

## 聯絡資訊

如有測試相關問題，請參考：
- 專案文件
- 程式碼註解
- 測試輸出日誌
