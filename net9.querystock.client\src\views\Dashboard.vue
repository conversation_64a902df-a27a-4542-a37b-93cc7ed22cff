<template>
  <div class="dashboard">
    <div class="dashboard-content">
      <div class="welcome-section">
        <h2>歡迎回來，{{ userName }}！</h2>
        <p>這是您的個人儀表板，您可以在這裡管理您的股票查詢和 API 金鑰。</p>
      </div>

      <!-- 快速統計 -->
      <div class="stats-grid">
        <div class="stat-card">
          <div class="stat-icon">📊</div>
          <div class="stat-content">
            <h3>今日查詢</h3>
            <p class="stat-number">{{ todayQueries }}</p>
          </div>
        </div>
        
        <div class="stat-card">
          <div class="stat-icon">🔑</div>
          <div class="stat-content">
            <h3>可用金鑰</h3>
            <p class="stat-number">{{ availableKeys }}</p>
          </div>
        </div>
        
        <div class="stat-card">
          <div class="stat-icon">⚡</div>
          <div class="stat-content">
            <h3>平均回應時間</h3>
            <p class="stat-number">{{ avgResponseTime }}ms</p>
          </div>
        </div>
        
        <div class="stat-card">
          <div class="stat-icon">✅</div>
          <div class="stat-content">
            <h3>成功率</h3>
            <p class="stat-number">{{ successRate }}%</p>
          </div>
        </div>
      </div>

      <!-- 快速操作 -->
      <div class="quick-actions">
        <h3>快速操作</h3>
        <div class="action-buttons">
          <router-link to="/" class="action-btn primary">
            <span class="btn-icon">🔍</span>
            股票查詢
          </router-link>
          <router-link to="/keys" class="action-btn secondary">
            <span class="btn-icon">🔑</span>
            管理金鑰
          </router-link>
          <button @click="refreshStats" class="action-btn tertiary" :disabled="loading">
            <span class="btn-icon">🔄</span>
            {{ loading ? '更新中...' : '刷新統計' }}
          </button>
        </div>
      </div>

      <!-- 最近活動 -->
      <div class="recent-activity">
        <h3>最近活動</h3>
        <div v-if="recentActivities.length === 0" class="no-activity">
          <p>暫無最近活動記錄</p>
        </div>
        <div v-else class="activity-list">
          <div v-for="activity in recentActivities" :key="activity.id" class="activity-item">
            <div class="activity-icon">
              <span v-if="activity.type === 'query'">🔍</span>
              <span v-else-if="activity.type === 'key'">🔑</span>
              <span v-else>📝</span>
            </div>
            <div class="activity-content">
              <p class="activity-title">{{ activity.title }}</p>
              <p class="activity-time">{{ formatTime(activity.time) }}</p>
            </div>
            <div class="activity-status" :class="activity.status">
              {{ activity.status === 'success' ? '成功' : '失敗' }}
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '../stores/auth'
import axios from 'axios'

export default {
  name: 'Dashboard',
  setup() {
    const router = useRouter()
    const authStore = useAuthStore()
    
    const loading = ref(false)
    const todayQueries = ref(0)
    const availableKeys = ref(0)
    const avgResponseTime = ref(0)
    const successRate = ref(0)
    const recentActivities = ref([])
    
    const userName = computed(() => authStore.userName)
    

    
    const refreshStats = async () => {
      loading.value = true
      try {
        // 獲取統計資料
        const response = await axios.get('/api/apikeymanagement/stats')
        const stats = response.data
        
        if (stats && stats.length > 0) {
          // 計算總統計
          availableKeys.value = stats.reduce((sum, provider) => sum + provider.availableKeys, 0)
          avgResponseTime.value = Math.round(
            stats.reduce((sum, provider) => sum + provider.averageResponseTimeMs, 0) / stats.length
          )
          successRate.value = Math.round(
            stats.reduce((sum, provider) => sum + provider.successRate, 0) / stats.length
          )
          todayQueries.value = stats.reduce((sum, provider) => sum + provider.todayUsageCount, 0)
        }
      } catch (error) {
        console.error('獲取統計資料失敗:', error)
      } finally {
        loading.value = false
      }
    }
    
    const formatTime = (time) => {
      return new Date(time).toLocaleString('zh-TW')
    }
    
    onMounted(() => {
      refreshStats()
    })
    
    return {
      userName,
      loading,
      todayQueries,
      availableKeys,
      avgResponseTime,
      successRate,
      recentActivities,
      refreshStats,
      formatTime
    }
  }
}
</script>

<style scoped>
.dashboard {
  min-height: 100vh;
}

.dashboard-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
}

.welcome-section {
  background: white;
  padding: 2rem;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  margin-bottom: 2rem;
}

.welcome-section h2 {
  margin: 0 0 1rem 0;
  color: #333;
  font-size: 2rem;
}

.welcome-section p {
  margin: 0;
  color: #666;
  font-size: 1.1rem;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.stat-card {
  background: white;
  padding: 1.5rem;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  gap: 1rem;
}

.stat-icon {
  font-size: 2.5rem;
}

.stat-content h3 {
  margin: 0 0 0.5rem 0;
  color: #666;
  font-size: 0.9rem;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.stat-number {
  margin: 0;
  color: #333;
  font-size: 2rem;
  font-weight: 700;
}

.quick-actions, .recent-activity {
  background: white;
  padding: 2rem;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  margin-bottom: 2rem;
}

.quick-actions h3, .recent-activity h3 {
  margin: 0 0 1.5rem 0;
  color: #333;
  font-size: 1.5rem;
}

.action-buttons {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
}

.action-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 1rem 1.5rem;
  border: none;
  border-radius: 8px;
  text-decoration: none;
  font-weight: 600;
  cursor: pointer;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.action-btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.action-btn.primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.action-btn.secondary {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  color: white;
}

.action-btn.tertiary {
  background: #f8f9fa;
  color: #333;
  border: 2px solid #e9ecef;
}

.action-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.btn-icon {
  font-size: 1.2rem;
}

.no-activity {
  text-align: center;
  padding: 2rem;
  color: #666;
}

.activity-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.activity-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background: #f8f9fa;
  border-radius: 8px;
}

.activity-icon {
  font-size: 1.5rem;
}

.activity-content {
  flex: 1;
}

.activity-title {
  margin: 0 0 0.25rem 0;
  font-weight: 600;
  color: #333;
}

.activity-time {
  margin: 0;
  font-size: 0.9rem;
  color: #666;
}

.activity-status {
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
  text-transform: uppercase;
}

.activity-status.success {
  background: #d4edda;
  color: #155724;
}

.activity-status.error {
  background: #f8d7da;
  color: #721c24;
}

@media (max-width: 768px) {
  .dashboard-content {
    padding: 1rem;
  }
  
  .stats-grid {
    grid-template-columns: 1fr;
  }
  
  .action-buttons {
    flex-direction: column;
  }
}
</style>
