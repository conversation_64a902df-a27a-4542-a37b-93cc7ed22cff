using Microsoft.EntityFrameworkCore;
using Net9.QueryStock.Server.Data;
using Net9.QueryStock.Server.Interfaces;
using Net9.QueryStock.Server.Models;

namespace Net9.QueryStock.Server.Services
{
    /// <summary>
    /// API 金鑰池服務實作
    /// </summary>
    public class ApiKeyPoolService : IApiKeyPoolService
    {
        private readonly Net9QueryStockServerContext _context;
        private readonly IApiKeyEncryptionService _encryptionService;
        private readonly ILogger<ApiKeyPoolService> _logger;
        private readonly SemaphoreSlim _semaphore = new(1, 1);

        public ApiKeyPoolService(
            Net9QueryStockServerContext context,
            IApiKeyEncryptionService encryptionService,
            ILogger<ApiKeyPoolService> logger)
        {
            _context = context;
            _encryptionService = encryptionService;
            _logger = logger;
        }

        /// <summary>
        /// 獲取可用的 API 金鑰
        /// </summary>
        public async Task<string?> GetAvailableApiKeyAsync(string providerName, CancellationToken cancellationToken = default)
        {
            await _semaphore.WaitAsync(cancellationToken);
            try
            {
                _logger.LogDebug("正在為提供者 {ProviderName} 獲取可用的 API 金鑰", providerName);

                var provider = await _context.Set<ApiKeyProvider>()
                    .Include(p => p.ApiKeys)
                    .FirstOrDefaultAsync(p => p.Name == providerName && p.IsEnabled, cancellationToken);

                if (provider == null)
                {
                    _logger.LogWarning("找不到啟用的服務提供者: {ProviderName}", providerName);
                    return null;
                }

                var now = DateTimeOffset.UtcNow;
                var availableKeys = provider.ApiKeys
                    .Where(k => k.IsAvailable)
                    .Where(k => !k.IsMinuteRateLimitExceeded(k.RateLimitPerMinute ?? provider.RateLimitPerMinute))
                    .Where(k => !k.IsDailyRateLimitExceeded(k.RateLimitPerDay ?? provider.RateLimitPerDay))
                    .OrderBy(k => k.Priority)
                    .ThenBy(k => k.TotalUsageCount)
                    .ToList();

                if (!availableKeys.Any())
                {
                    _logger.LogWarning("沒有可用的 API 金鑰用於提供者: {ProviderName}", providerName);
                    return null;
                }

                var selectedKey = availableKeys.First();
                
                // 更新使用統計
                await UpdateKeyUsageCountersAsync(selectedKey, now);
                
                var decryptedKey = _encryptionService.DecryptKey(selectedKey.EncryptedKey);
                
                _logger.LogDebug("為提供者 {ProviderName} 選擇了金鑰: {KeyName}", providerName, selectedKey.Name);
                
                return decryptedKey;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "獲取 API 金鑰時發生錯誤: {ProviderName}", providerName);
                return null;
            }
            finally
            {
                _semaphore.Release();
            }
        }

        /// <summary>
        /// 記錄 API 金鑰使用情況
        /// </summary>
        public async Task<bool> RecordUsageAsync(
            string providerName,
            string apiKey,
            string endpoint,
            bool isSuccess,
            int responseTimeMs,
            string? errorMessage = null,
            int? httpStatusCode = null,
            CancellationToken cancellationToken = default)
        {
            try
            {
                var keyEntity = await FindApiKeyByValueAsync(providerName, apiKey, cancellationToken);
                if (keyEntity == null)
                {
                    _logger.LogWarning("找不到對應的 API 金鑰記錄");
                    return false;
                }

                // 建立使用記錄
                var usage = new ApiKeyUsage
                {
                    Id = Guid.NewGuid(),
                    ApiKeyId = keyEntity.Id,
                    Endpoint = endpoint,
                    IsSuccess = isSuccess,
                    ResponseTimeMs = responseTimeMs,
                    ErrorMessage = errorMessage,
                    HttpStatusCode = httpStatusCode,
                    RequestTime = DateTimeOffset.UtcNow
                };

                _context.Set<ApiKeyUsage>().Add(usage);

                // 更新金鑰統計
                keyEntity.LastUsedAt = DateTimeOffset.UtcNow;
                keyEntity.TotalUsageCount++;

                if (!isSuccess)
                {
                    keyEntity.ConsecutiveErrorCount++;
                    keyEntity.LastErrorAt = DateTimeOffset.UtcNow;
                    keyEntity.LastErrorMessage = errorMessage;

                    // 如果連續錯誤次數過多，暫停金鑰
                    if (keyEntity.ConsecutiveErrorCount >= 5)
                    {
                        var suspendMinutes = Math.Min(keyEntity.ConsecutiveErrorCount * 5, 60); // 最多暫停 1 小時
                        keyEntity.SuspendedUntil = DateTimeOffset.UtcNow.AddMinutes(suspendMinutes);
                        keyEntity.Status = ApiKeyStatus.Suspended;
                        
                        _logger.LogWarning("API 金鑰 {KeyName} 因連續錯誤被暫停 {Minutes} 分鐘", 
                            keyEntity.Name, suspendMinutes);
                    }
                }
                else
                {
                    // 成功時重置錯誤計數
                    keyEntity.ConsecutiveErrorCount = 0;
                    keyEntity.LastErrorMessage = null;
                }

                keyEntity.UpdatedAt = DateTimeOffset.UtcNow;

                await _context.SaveChangesAsync(cancellationToken);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "記錄 API 金鑰使用情況時發生錯誤");
                return false;
            }
        }

        /// <summary>
        /// 標記 API 金鑰為錯誤狀態
        /// </summary>
        public async Task<bool> MarkKeyAsErrorAsync(
            string providerName,
            string apiKey,
            string errorMessage,
            int? suspendDurationMinutes = null,
            CancellationToken cancellationToken = default)
        {
            try
            {
                var keyEntity = await FindApiKeyByValueAsync(providerName, apiKey, cancellationToken);
                if (keyEntity == null)
                {
                    _logger.LogWarning("找不到對應的 API 金鑰記錄");
                    return false;
                }

                keyEntity.ConsecutiveErrorCount++;
                keyEntity.LastErrorAt = DateTimeOffset.UtcNow;
                keyEntity.LastErrorMessage = errorMessage;

                var suspendMinutes = suspendDurationMinutes ?? Math.Min(keyEntity.ConsecutiveErrorCount * 10, 120);
                keyEntity.SuspendedUntil = DateTimeOffset.UtcNow.AddMinutes(suspendMinutes);
                keyEntity.Status = ApiKeyStatus.Suspended;
                keyEntity.UpdatedAt = DateTimeOffset.UtcNow;

                await _context.SaveChangesAsync(cancellationToken);

                _logger.LogWarning("API 金鑰 {KeyName} 被標記為錯誤並暫停 {Minutes} 分鐘: {Error}", 
                    keyEntity.Name, suspendMinutes, errorMessage);

                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "標記 API 金鑰錯誤狀態時發生錯誤");
                return false;
            }
        }

        /// <summary>
        /// 重置 API 金鑰錯誤狀態
        /// </summary>
        public async Task<bool> ResetKeyErrorStatusAsync(
            string providerName,
            string apiKey,
            CancellationToken cancellationToken = default)
        {
            try
            {
                var keyEntity = await FindApiKeyByValueAsync(providerName, apiKey, cancellationToken);
                if (keyEntity == null)
                {
                    _logger.LogWarning("找不到對應的 API 金鑰記錄");
                    return false;
                }

                keyEntity.ConsecutiveErrorCount = 0;
                keyEntity.LastErrorMessage = null;
                keyEntity.SuspendedUntil = null;
                keyEntity.Status = ApiKeyStatus.Active;
                keyEntity.UpdatedAt = DateTimeOffset.UtcNow;

                await _context.SaveChangesAsync(cancellationToken);

                _logger.LogInformation("API 金鑰 {KeyName} 的錯誤狀態已重置", keyEntity.Name);

                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "重置 API 金鑰錯誤狀態時發生錯誤");
                return false;
            }
        }

        /// <summary>
        /// 更新金鑰使用計數器
        /// </summary>
        private async Task UpdateKeyUsageCountersAsync(ApiKey key, DateTimeOffset now)
        {
            var today = now.Date;
            var currentMinute = new DateTimeOffset(now.Year, now.Month, now.Day, now.Hour, now.Minute, 0, now.Offset);

            // 重置每日計數器
            if (key.LastUsedAt?.Date != today)
            {
                key.TodayUsageCount = 0;
            }

            // 重置分鐘計數器
            if (key.CurrentMinuteStartTime < currentMinute)
            {
                key.CurrentMinuteUsageCount = 0;
                key.CurrentMinuteStartTime = currentMinute;
            }

            key.CurrentMinuteUsageCount++;
            key.TodayUsageCount++;
            key.LastUsedAt = now;
            key.UpdatedAt = now;

            await _context.SaveChangesAsync();
        }

        /// <summary>
        /// 獲取服務提供者的統計資訊
        /// </summary>
        public async Task<ApiKeyPoolStats?> GetProviderStatsAsync(string providerName, CancellationToken cancellationToken = default)
        {
            try
            {
                var provider = await _context.Set<ApiKeyProvider>()
                    .Include(p => p.ApiKeys)
                    .ThenInclude(k => k.UsageRecords.Where(u => u.RequestTime.Date == DateTime.Today))
                    .FirstOrDefaultAsync(p => p.Name == providerName, cancellationToken);

                if (provider == null)
                    return null;

                var stats = new ApiKeyPoolStats
                {
                    ProviderName = providerName,
                    TotalKeys = provider.ApiKeys.Count,
                    AvailableKeys = provider.ApiKeys.Count(k => k.IsAvailable),
                    SuspendedKeys = provider.ApiKeys.Count(k => k.Status == ApiKeyStatus.Suspended),
                    DisabledKeys = provider.ApiKeys.Count(k => k.Status == ApiKeyStatus.Disabled),
                    TodayUsageCount = provider.ApiKeys.Sum(k => k.TodayUsageCount),
                    CurrentMinuteUsageCount = provider.ApiKeys.Sum(k => k.CurrentMinuteUsageCount),
                    LastUsedAt = provider.ApiKeys.Max(k => k.LastUsedAt)
                };

                // 計算平均回應時間和成功率
                var todayUsages = provider.ApiKeys.SelectMany(k => k.UsageRecords).ToList();
                if (todayUsages.Any())
                {
                    stats.AverageResponseTimeMs = todayUsages.Average(u => u.ResponseTimeMs);
                    stats.SuccessRate = (double)todayUsages.Count(u => u.IsSuccess) / todayUsages.Count * 100;
                }

                return stats;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "獲取提供者統計資訊時發生錯誤: {ProviderName}", providerName);
                return null;
            }
        }

        /// <summary>
        /// 獲取所有服務提供者的統計資訊
        /// </summary>
        public async Task<IEnumerable<ApiKeyPoolStats>> GetAllProvidersStatsAsync(CancellationToken cancellationToken = default)
        {
            try
            {
                var providers = await _context.Set<ApiKeyProvider>()
                    .Where(p => p.IsEnabled)
                    .Select(p => p.Name)
                    .ToListAsync(cancellationToken);

                var statsList = new List<ApiKeyPoolStats>();
                foreach (var providerName in providers)
                {
                    var stats = await GetProviderStatsAsync(providerName, cancellationToken);
                    if (stats != null)
                        statsList.Add(stats);
                }

                return statsList;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "獲取所有提供者統計資訊時發生錯誤");
                return Enumerable.Empty<ApiKeyPoolStats>();
            }
        }

        /// <summary>
        /// 新增 API 金鑰
        /// </summary>
        public async Task<bool> AddApiKeyAsync(
            string providerName,
            string keyName,
            string plainKey,
            int priority = 1,
            int? rateLimitPerMinute = null,
            int? rateLimitPerDay = null,
            DateTimeOffset? expiresAt = null,
            CancellationToken cancellationToken = default)
        {
            try
            {
                var provider = await _context.Set<ApiKeyProvider>()
                    .FirstOrDefaultAsync(p => p.Name == providerName, cancellationToken);

                if (provider == null)
                {
                    _logger.LogWarning("找不到服務提供者: {ProviderName}", providerName);
                    return false;
                }

                var encryptedKey = _encryptionService.EncryptKey(plainKey);

                var apiKey = new ApiKey
                {
                    Id = Guid.NewGuid(),
                    ProviderId = provider.Id,
                    Name = keyName,
                    EncryptedKey = encryptedKey,
                    Status = ApiKeyStatus.Active,
                    Priority = priority,
                    RateLimitPerMinute = rateLimitPerMinute,
                    RateLimitPerDay = rateLimitPerDay,
                    ExpiresAt = expiresAt,
                    CreatedAt = DateTimeOffset.UtcNow,
                    UpdatedAt = DateTimeOffset.UtcNow
                };

                _context.Set<ApiKey>().Add(apiKey);
                await _context.SaveChangesAsync(cancellationToken);

                _logger.LogInformation("成功新增 API 金鑰: {KeyName} 到提供者: {ProviderName}", keyName, providerName);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "新增 API 金鑰時發生錯誤");
                return false;
            }
        }

        /// <summary>
        /// 更新 API 金鑰狀態
        /// </summary>
        public async Task<bool> UpdateKeyStatusAsync(Guid keyId, ApiKeyStatus status, CancellationToken cancellationToken = default)
        {
            try
            {
                var apiKey = await _context.Set<ApiKey>()
                    .FirstOrDefaultAsync(k => k.Id == keyId, cancellationToken);

                if (apiKey == null)
                {
                    _logger.LogWarning("找不到 API 金鑰: {KeyId}", keyId);
                    return false;
                }

                apiKey.Status = status;
                apiKey.UpdatedAt = DateTimeOffset.UtcNow;

                // 如果設為啟用，清除暫停狀態
                if (status == ApiKeyStatus.Active)
                {
                    apiKey.SuspendedUntil = null;
                    apiKey.ConsecutiveErrorCount = 0;
                    apiKey.LastErrorMessage = null;
                }

                await _context.SaveChangesAsync(cancellationToken);

                _logger.LogInformation("成功更新 API 金鑰狀態: {KeyId} -> {Status}", keyId, status);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新 API 金鑰狀態時發生錯誤");
                return false;
            }
        }

        /// <summary>
        /// 刪除 API 金鑰
        /// </summary>
        public async Task<bool> DeleteApiKeyAsync(Guid keyId, CancellationToken cancellationToken = default)
        {
            try
            {
                var apiKey = await _context.Set<ApiKey>()
                    .FirstOrDefaultAsync(k => k.Id == keyId, cancellationToken);

                if (apiKey == null)
                {
                    _logger.LogWarning("找不到 API 金鑰: {KeyId}", keyId);
                    return false;
                }

                _context.Set<ApiKey>().Remove(apiKey);
                await _context.SaveChangesAsync(cancellationToken);

                _logger.LogInformation("成功刪除 API 金鑰: {KeyId}", keyId);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "刪除 API 金鑰時發生錯誤");
                return false;
            }
        }

        /// <summary>
        /// 根據金鑰值查找 API 金鑰實體
        /// </summary>
        private async Task<ApiKey?> FindApiKeyByValueAsync(string providerName, string apiKey, CancellationToken cancellationToken)
        {
            var provider = await _context.Set<ApiKeyProvider>()
                .Include(p => p.ApiKeys)
                .FirstOrDefaultAsync(p => p.Name == providerName, cancellationToken);

            if (provider == null)
                return null;

            foreach (var key in provider.ApiKeys)
            {
                try
                {
                    var decryptedKey = _encryptionService.DecryptKey(key.EncryptedKey);
                    if (decryptedKey == apiKey)
                        return key;
                }
                catch
                {
                    // 忽略解密錯誤，繼續下一個
                    continue;
                }
            }

            return null;
        }
    }
}
