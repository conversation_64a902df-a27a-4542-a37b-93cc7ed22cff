using Net9.QueryStock.Server.DTOs;
using Net9.QueryStock.Server.Models;

namespace Net9.QueryStock.Server.Interfaces
{
    /// <summary>
    /// 認證服務介面
    /// </summary>
    public interface IAuthService
    {
        /// <summary>
        /// 用戶註冊
        /// </summary>
        /// <param name="request">註冊請求</param>
        /// <param name="cancellationToken">取消權杖</param>
        /// <returns>認證回應</returns>
        Task<AuthResponse> RegisterAsync(RegisterRequest request, CancellationToken cancellationToken = default);
        
        /// <summary>
        /// 用戶登入
        /// </summary>
        /// <param name="request">登入請求</param>
        /// <param name="cancellationToken">取消權杖</param>
        /// <returns>認證回應</returns>
        Task<AuthResponse> LoginAsync(LoginRequest request, CancellationToken cancellationToken = default);
        
        /// <summary>
        /// 驗證權杖
        /// </summary>
        /// <param name="token">JWT 權杖</param>
        /// <returns>用戶資訊，如果權杖無效則返回 null</returns>
        Task<UserDto?> ValidateTokenAsync(string token);
        
        /// <summary>
        /// 根據用戶 ID 獲取用戶資訊
        /// </summary>
        /// <param name="userId">用戶 ID</param>
        /// <param name="cancellationToken">取消權杖</param>
        /// <returns>用戶資訊</returns>
        Task<UserDto?> GetUserByIdAsync(Guid userId, CancellationToken cancellationToken = default);
        
        /// <summary>
        /// 修改密碼
        /// </summary>
        /// <param name="userId">用戶 ID</param>
        /// <param name="request">修改密碼請求</param>
        /// <param name="cancellationToken">取消權杖</param>
        /// <returns>是否成功</returns>
        Task<AuthResponse> ChangePasswordAsync(Guid userId, ChangePasswordRequest request, CancellationToken cancellationToken = default);
        
        /// <summary>
        /// 生成 JWT 權杖
        /// </summary>
        /// <param name="user">用戶資訊</param>
        /// <returns>JWT 權杖和過期時間</returns>
        (string Token, DateTimeOffset ExpiresAt) GenerateJwtToken(User user);
        
        /// <summary>
        /// 驗證密碼
        /// </summary>
        /// <param name="password">明文密碼</param>
        /// <param name="hash">密碼雜湊值</param>
        /// <param name="salt">鹽值</param>
        /// <returns>是否匹配</returns>
        bool VerifyPassword(string password, string hash, string salt);
        
        /// <summary>
        /// 雜湊密碼
        /// </summary>
        /// <param name="password">明文密碼</param>
        /// <returns>雜湊值和鹽值</returns>
        (string Hash, string Salt) HashPassword(string password);
    }
}
