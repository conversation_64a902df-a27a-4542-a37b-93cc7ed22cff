using Microsoft.Extensions.Caching.Memory;
using System.Text.Json;
using Net9.QueryStock.Server.Interfaces;

namespace Net9.QueryStock.Server.Services
{
    /// <summary>
    /// 記憶體快取服務實作
    /// </summary>
    public class MemoryCacheService : ICacheService
    {
        private readonly IMemoryCache _memoryCache;
        private readonly ILogger<MemoryCacheService> _logger;
        private readonly JsonSerializerOptions _jsonOptions;

        public MemoryCacheService(
            IMemoryCache memoryCache,
            ILogger<MemoryCacheService> logger)
        {
            _memoryCache = memoryCache;
            _logger = logger;
            _jsonOptions = new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true
            };
        }

        /// <summary>
        /// 獲取快取資料
        /// </summary>
        public Task<T?> GetAsync<T>(string key, CancellationToken cancellationToken = default) where T : class
        {
            try
            {
                if (_memoryCache.TryGetValue(key, out var cachedValue))
                {
                    if (cachedValue is T directValue)
                    {
                        _logger.LogDebug("快取命中: {Key}", key);
                        return Task.FromResult<T?>(directValue);
                    }
                    
                    if (cachedValue is string jsonString)
                    {
                        var deserializedValue = JsonSerializer.Deserialize<T>(jsonString, _jsonOptions);
                        _logger.LogDebug("快取命中 (JSON): {Key}", key);
                        return Task.FromResult(deserializedValue);
                    }
                }
                
                _logger.LogDebug("快取未命中: {Key}", key);
                return Task.FromResult<T?>(null);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "獲取快取資料時發生錯誤: {Key}", key);
                return Task.FromResult<T?>(null);
            }
        }

        /// <summary>
        /// 設定快取資料
        /// </summary>
        public Task<bool> SetAsync<T>(string key, T value, TimeSpan? expiration = null, CancellationToken cancellationToken = default) where T : class
        {
            try
            {
                var options = new MemoryCacheEntryOptions();
                
                if (expiration.HasValue)
                {
                    options.AbsoluteExpirationRelativeToNow = expiration.Value;
                }
                else
                {
                    // 預設過期時間為 5 分鐘
                    options.AbsoluteExpirationRelativeToNow = TimeSpan.FromMinutes(5);
                }
                
                // 設定優先級，當記憶體不足時會優先移除低優先級的項目
                options.Priority = CacheItemPriority.Normal;
                
                _memoryCache.Set(key, value, options);
                _logger.LogDebug("快取已設定: {Key}, 過期時間: {Expiration}", key, options.AbsoluteExpirationRelativeToNow);
                
                return Task.FromResult(true);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "設定快取資料時發生錯誤: {Key}", key);
                return Task.FromResult(false);
            }
        }

        /// <summary>
        /// 移除快取資料
        /// </summary>
        public Task<bool> RemoveAsync(string key, CancellationToken cancellationToken = default)
        {
            try
            {
                _memoryCache.Remove(key);
                _logger.LogDebug("快取已移除: {Key}", key);
                return Task.FromResult(true);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "移除快取資料時發生錯誤: {Key}", key);
                return Task.FromResult(false);
            }
        }

        /// <summary>
        /// 檢查快取是否存在
        /// </summary>
        public Task<bool> ExistsAsync(string key, CancellationToken cancellationToken = default)
        {
            try
            {
                var exists = _memoryCache.TryGetValue(key, out _);
                return Task.FromResult(exists);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "檢查快取存在時發生錯誤: {Key}", key);
                return Task.FromResult(false);
            }
        }

        /// <summary>
        /// 獲取或設定快取資料
        /// </summary>
        public async Task<T?> GetOrSetAsync<T>(
            string key, 
            Func<Task<T?>> factory, 
            TimeSpan? expiration = null, 
            CancellationToken cancellationToken = default) where T : class
        {
            try
            {
                // 先嘗試從快取獲取
                var cachedValue = await GetAsync<T>(key, cancellationToken);
                if (cachedValue != null)
                {
                    return cachedValue;
                }
                
                // 快取未命中，執行工廠方法獲取資料
                _logger.LogDebug("快取未命中，正在執行工廠方法: {Key}", key);
                var value = await factory();
                
                if (value != null)
                {
                    // 將新資料存入快取
                    await SetAsync(key, value, expiration, cancellationToken);
                }
                
                return value;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "GetOrSet 操作時發生錯誤: {Key}", key);
                return null;
            }
        }

        /// <summary>
        /// 批量移除快取資料（記憶體快取不支援模式匹配，此方法返回 0）
        /// </summary>
        public Task<int> RemoveByPatternAsync(string pattern, CancellationToken cancellationToken = default)
        {
            _logger.LogWarning("記憶體快取不支援模式匹配移除，模式: {Pattern}", pattern);
            return Task.FromResult(0);
        }
    }
}
